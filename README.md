# ESP32 智能时钟项目

一个功能完整的ESP32智能时钟，具有WiFi时间同步、多闹钟、电子墨水屏显示等功能。

## 🎯 功能特性

### ⏰ 时钟功能
- **网络时间同步** - 自动从NTP服务器获取准确时间
- **中国时区支持** - 自动设置GMT+8时区
- **实时显示** - 电子墨水屏显示时间和日期
- **断电记忆** - 设置永久保存

### 🔔 闹钟系统
- **3个独立闹钟** - 可分别设置时间和开关
- **智能提醒** - 蜂鸣器音效 + LED闪烁
- **简单设置** - 触摸按键直观操作
- **数据持久化** - 闹钟设置断电不丢失

### 📱 用户界面
- **2.9寸电子墨水屏** - 清晰显示，低功耗
- **多界面切换** - 时间/闹钟界面自由切换
- **状态指示** - WiFi连接、闹钟状态一目了然
- **触摸按键** - 3个电容式触摸按键

### 🌈 视觉反馈
- **WS2812B RGB LED** - 状态指示和闹钟提醒
- **启动动画** - 彩色LED启动序列
- **时间指示** - 每秒蓝色闪烁

## 🛠️ 硬件配置

### 主要组件
- **主控**: ESP32-WROOM-32
- **显示**: 2.9寸电子墨水屏（黑白）
- **LED**: WS2812B RGB LED
- **按键**: 3个触摸按键
- **蜂鸣器**: 无源蜂鸣器
- **接口**: USB-C（支持串口通信）
- **电源**: TP4056充电管理 + 3.7V 1200mAh电池

### GPIO引脚定义
| 功能 | GPIO | 说明 |
|------|------|------|
| 电子墨水屏 | CS:26, DC:27, RST:14, BUSY:12 | SPI接口 |
| | SCK:18, MOSI:23 | SPI时钟和数据 |
| WS2812B LED | 16 | RGB LED控制 |
| 蜂鸣器 | 25 | 无源蜂鸣器 |
| 触摸按键 | A:33, B:13, C:5 | 电容式触摸 |

## 📁 项目结构

```
src/
├── main.cpp           # 主程序入口（简洁版）
├── config.h           # 配置和常量定义
├── hardware.h/cpp     # 硬件初始化和控制
├── wifi_manager.h/cpp # WiFi和时间同步
├── alarm.h/cpp        # 闹钟功能
├── display.h/cpp      # 显示相关功能
└── storage.h/cpp      # 数据存储功能
```

### 模块化设计优势
- **代码组织清晰** - 每个功能独立文件
- **易于维护** - 修改某个功能不影响其他部分
- **团队协作友好** - 多人可以同时开发不同模块
- **代码复用** - 模块可以在其他项目中复用

## 🎮 操作说明

### 按键功能

**正常模式**:
- **按键A**: 切换显示模式（时间 → 闹钟1 → 闹钟2 → 闹钟3）
- **按键B**: 开启/关闭当前闹钟
- **按键C**: 进入设置模式

**设置模式**:
- **按键A**: 切换设置项（小时 → 分钟 → 退出）
- **按键B**: 增加数值
- **按键C**: 减少数值

**闹钟响铃时**:
- **任意按键**: 停止闹钟

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装PlatformIO
pip install platformio

# 克隆项目
git clone <项目地址>
cd esp32-clock
```

### 2. 配置WiFi
编辑 `src/wifi_manager.cpp`，修改WiFi信息：
```cpp
const char* ssid = "您的WiFi名称";
const char* password = "您的WiFi密码";
```

### 3. 编译和烧录
```bash
# 编译项目
pio run

# 烧录到ESP32
pio run --target upload

# 查看串口输出
pio device monitor --port /dev/cu.usbserial-* --baud 115200
```

## 🔧 开发说明

### 添加新功能
1. 在相应的模块文件中添加功能
2. 在头文件中声明接口
3. 在main.cpp中调用

### 调试技巧
- 使用串口监视器查看运行状态
- LED颜色指示不同的系统状态
- 每10秒自动打印系统状态信息

## 📚 学习价值

这个项目涵盖了嵌入式开发的多个重要概念：
- **硬件控制** - GPIO、SPI、PWM
- **网络编程** - WiFi连接、NTP时间同步
- **用户界面** - 显示控制、按键处理
- **数据管理** - 持久化存储、状态管理
- **系统设计** - 模块化架构、任务调度

## 🎓 作者

ESP32学习项目 - 从零开始的嵌入式开发之旅

## 📄 许可证

MIT License - 自由使用和修改
