[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; 串口监视器波特率
monitor_speed = 115200

; 库依赖
lib_deps = 
    zinggjm/GxEPD2@^1.5.7
    adafruit/Adafruit GFX Library@^1.11.9

; 编译选项
build_flags = 
    -DCORE_DEBUG_LEVEL=3
    -DBOARD_HAS_PSRAM

; 上传设置
upload_speed = 921600

; 串口设置（如果需要指定特定串口，取消注释并修改）
; upload_port = /dev/cu.usbserial-*
; monitor_port = /dev/cu.usbserial-*

; 分区表设置（如果需要更大的应用程序空间）
; board_build.partitions = huge_app.csv
