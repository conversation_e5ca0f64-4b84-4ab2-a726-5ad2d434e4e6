.pio/build/esp32dev/libcf8/FastLED/platforms/esp/32/spi_ws2812/strip_spi.cpp.o: \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/spi_ws2812/strip_spi.cpp \
 .pio/libdeps/esp32dev/FastLED/src/third_party/espressif/led_strip/src/enabled.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/dio_qspi/include/sdkconfig.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/esp_version.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_idf_version.h
