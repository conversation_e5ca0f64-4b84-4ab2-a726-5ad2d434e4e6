.pio/build/esp32dev/libcf8/FastLED/fx/video/frame_interpolator.cpp.o: \
 .pio/libdeps/esp32dev/FastLED/src/fx/video/frame_interpolator.cpp \
 .pio/libdeps/esp32dev/FastLED/src/fx/video/frame_interpolator.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/assert.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/assert_defs.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/strstream.h \
 .pio/libdeps/esp32dev/FastLED/src/crgb.h \
 .pio/libdeps/esp32dev/FastLED/src/chsv.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/namespace.h \
 .pio/libdeps/esp32dev/FastLED/src/color.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/types.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/force_inline.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/template_magic.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/type_traits.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/str.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/geometry.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/clamp.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map_range.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math_macros.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/scoped_ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/allocator.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/inplacenew.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/unused.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/deprecated.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/vector.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/functional.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/insert_result.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/warn.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/dbg.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/esp_assert.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp_rom_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/reset_reasons.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp32/rom/ets_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log_internal.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/pair.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/frame.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xymap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/lut.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xmap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/draw_mode.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/video/frame_tracker.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/video/pixel_stream.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/bytestream.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/file_system.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/video.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/fx1d.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/fx.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/detail/draw_context.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/detail/transition.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/time.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/circular_buffer.h
