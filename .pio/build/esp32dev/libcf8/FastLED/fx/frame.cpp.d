.pio/build/esp32dev/libcf8/FastLED/fx/frame.cpp.o: \
 .pio/libdeps/esp32dev/FastLED/src/fx/frame.cpp \
 .pio/libdeps/esp32dev/FastLED/src/crgb.h \
 .pio/libdeps/esp32dev/FastLED/src/chsv.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/namespace.h \
 .pio/libdeps/esp32dev/FastLED/src/color.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/types.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/force_inline.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/template_magic.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/type_traits.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/allocator.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/inplacenew.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/unused.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/dbg.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/strstream.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/str.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/geometry.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/clamp.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map_range.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math_macros.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/scoped_ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/deprecated.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/vector.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/functional.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/insert_result.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/warn.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xymap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/lut.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xmap.h \
 .pio/libdeps/esp32dev/FastLED/src/fx/frame.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/draw_mode.h
