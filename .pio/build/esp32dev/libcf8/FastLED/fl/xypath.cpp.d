.pio/build/esp32dev/libcf8/FastLED/fl/xypath.cpp.o: \
 .pio/libdeps/esp32dev/FastLED/src/fl/xypath.cpp \
 .pio/libdeps/esp32dev/FastLED/src/fl/assert.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/assert_defs.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/strstream.h \
 .pio/libdeps/esp32dev/FastLED/src/crgb.h \
 .pio/libdeps/esp32dev/FastLED/src/chsv.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/namespace.h \
 .pio/libdeps/esp32dev/FastLED/src/color.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/types.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/force_inline.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/template_magic.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/type_traits.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/str.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/geometry.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/clamp.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map_range.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math_macros.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/scoped_ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/allocator.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/inplacenew.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/unused.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/deprecated.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/vector.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/functional.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/insert_result.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/warn.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/dbg.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/esp_assert.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp_rom_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/reset_reasons.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp32/rom/ets_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log_internal.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/function.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/gradient.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/colorutils.h \
 .pio/libdeps/esp32dev/FastLED/src/fastled_progmem.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/null_progmem.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/blur.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/colorutils_misc.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/fill.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xymap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/lut.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xmap.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/memmove.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/slice.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/variant.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/raster.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/raster_sparse.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/grid.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/hash_map.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/bitset.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/bitset_dynamic.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/hash.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/optional.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/pair.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/tile2x2.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xypath.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/leds.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/transform.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xypath_impls.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xypath_renderer.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/thread_local.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/thread.h
