.pio/build/esp32dev/src/main.cpp.o: src/main.cpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Arduino.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp_arduino_version.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/FreeRTOS.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_compiler.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/FreeRTOSConfig_arch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/xtensa_config.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa-versions.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core-isa.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core-matmap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/tie.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/system.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/corebits.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime-frames.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp_rom_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/reset_reasons.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp32/rom/ets_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/projdefs.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/portable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/deprecated_definitions.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portmacro.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/specreg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime-core-state.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xt_instr_macros.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/spinlock.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/cpu_hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_err.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_compiler.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/soc_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/cpu_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/esp32/include/hal/cpu_ll.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_attr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/extreg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_bit_defs.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/compare_set.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/include/soc/soc_memory_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/soc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_assert.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_system/include/esp_private/crosscore_int.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_timer/include/esp_timer.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/newlib/platform_include/esp_newlib.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/heap/include/esp_heap_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/heap/include/multi_heap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_system/include/esp_system.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_idf_version.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_mac.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_chip_info.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_random.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portbenchmark.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_api.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portmacro_deprecated.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/mpu_wrappers.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/task.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/list.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/esp_additions/freertos/task_snapshot.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/semphr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/queue.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/task.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_sleep.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/touch_sensor_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/gpio_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/include/soc/gpio_periph.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/io_mux_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/gpio_struct.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/gpio_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/gpio_sig_map.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/queue.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/event_groups.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/timers.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-log.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log_internal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-matrix.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-uart.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/uart_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/variants/esp32/pins_arduino.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-touch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-dac.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/driver/include/driver/gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_intr_alloc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp32/rom/gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-adc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-spi.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-i2c.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-ledc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-rmt.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-sigmadelta.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-timer.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-bt.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-psram.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-rgb-led.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp8266-compat.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/stdlib_noniso.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/binary.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WCharacter.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WString.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/pgmspace.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Stream.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Print.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Printable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPAddress.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WString.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Printable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Client.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Server.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Udp.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Stream.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPAddress.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/HardwareSerial.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/HWCDC.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/USBCDC.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Esp.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/spi_flash/include/esp_partition.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/spi_flash/include/esp_flash.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/spi_flash_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/esp_flash_err.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/spi_flash/include/esp_spi_flash.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/spi_flash/include/esp_spi_flash_counters.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/esp32/spiram.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/io_pin_remap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Arduino.h \
 .pio/libdeps/esp32dev/GxEPD2/src/GxEPD2_BW.h \
 .pio/libdeps/esp32dev/Adafruit\ GFX\ Library/Adafruit_GFX.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Print.h \
 .pio/libdeps/esp32dev/Adafruit\ GFX\ Library/gfxfont.h \
 .pio/libdeps/esp32dev/Adafruit\ BusIO/Adafruit_I2CDevice.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Wire/src/Wire.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal.h \
 .pio/libdeps/esp32dev/Adafruit\ BusIO/Adafruit_SPIDevice.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SPI/src/SPI.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-spi.h \
 .pio/libdeps/esp32dev/GxEPD2/src/GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/GxEPD2.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_102.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_150_BN.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_154.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_154_D67.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_154_T8.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_154_M09.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_154_M10.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_154_GDEY0154D67.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_213.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_213_B72.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_213_B73.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_213_B74.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_213_flex.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_213_M21.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_213_T5D.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_213_BN.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_213_GDEY0213B74.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_260.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_260_M01.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_266_BN.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_266_GDEY0266T90.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_290.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_290_T5.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_290_T5D.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_290_I6FD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_290_M06.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_290_T94.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_290_GDEY029T94.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_290_T94_V2.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_290_BS.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_290_GDEY029T71H.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_270.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_270_GDEY027T91.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdeq/GxEPD2_310_GDEQ031T10.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdeq/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_371.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_370_TC1.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_370_GDEY037T03.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_420.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_420_M01.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_420_GDEY042T81.h \
 .pio/libdeps/esp32dev/GxEPD2/src/other/GxEPD2_420_GYE042A87.h \
 .pio/libdeps/esp32dev/GxEPD2/src/other/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/other/GxEPD2_420_SE0420NQ04.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdeq/GxEPD2_426_GDEQ0426T82.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_579_GDEY0579T93.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_583.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_583_T8.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdeq/GxEPD2_583_GDEQ0583T31.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_750.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_750_T7.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey/GxEPD2_750_GDEY075T7.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdem/GxEPD2_1020_GDEM102T91.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdem/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdem/GxEPD2_1085_GDEM1085T51.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_1160_T91.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdem/GxEPD2_1330_GDEM133T91.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd/GxEPD2_1248.h \
 .pio/libdeps/esp32dev/GxEPD2/src/it8951/GxEPD2_it60.h \
 .pio/libdeps/esp32dev/GxEPD2/src/it8951/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/it8951/GxEPD2_it60_1448x1072.h \
 .pio/libdeps/esp32dev/GxEPD2/src/it8951/GxEPD2_it78_1872x1404.h \
 .pio/libdeps/esp32dev/GxEPD2/src/it8951/GxEPD2_it103_1872x1404.h \
 .pio/libdeps/esp32dev/GxEPD2/src/GxEPD2_3C.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_154c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_154_Z90c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_213c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_213_Z19c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_213_Z98c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_290c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_290_Z13c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_290_C90c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_266c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_270c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_420c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_420c_Z21.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey3c/GxEPD2_420c_GDEY042Z98.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey3c/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey3c/GxEPD2_579c_GDEY0579Z93.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_583c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdeq3c/GxEPD2_583c_GDEQ0583Z31.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdeq3c/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_583c_Z83.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_750c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_750c_Z08.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_750c_Z90.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdey3c/GxEPD2_1160c_GDEY116Z91.h \
 .pio/libdeps/esp32dev/GxEPD2/src/epd3c/GxEPD2_1248c.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdem3c/GxEPD2_1330c_GDEM133Z91.h \
 .pio/libdeps/esp32dev/GxEPD2/src/gdem3c/../GxEPD2_EPD.h \
 .pio/libdeps/esp32dev/Adafruit\ GFX\ Library/Fonts/FreeMonoBold12pt7b.h \
 .pio/libdeps/esp32dev/FastLED/src/FastLED.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/force_inline.h \
 .pio/libdeps/esp32dev/FastLED/src/cpp_compat.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/register.h \
 .pio/libdeps/esp32dev/FastLED/src/fastled_config.h \
 .pio/libdeps/esp32dev/FastLED/src/led_sysdefs.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/led_sysdefs_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/namespace.h \
 .pio/libdeps/esp32dev/FastLED/src/fastled_delay.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/types.h \
 .pio/libdeps/esp32dev/FastLED/src/bitswap.h \
 .pio/libdeps/esp32dev/FastLED/src/controller.h \
 .pio/libdeps/esp32dev/FastLED/src/cpixel_ledcontroller.h \
 .pio/libdeps/esp32dev/FastLED/src/pixeltypes.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/types.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/lib8static.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/qfx.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/memmove.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/math8.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/scale8.h \
 .pio/libdeps/esp32dev/FastLED/src/crgb.h \
 .pio/libdeps/esp32dev/FastLED/src/chsv.h \
 .pio/libdeps/esp32dev/FastLED/src/color.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/template_magic.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/type_traits.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/intmap.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/random8.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/trig8.h \
 .pio/libdeps/esp32dev/FastLED/src/eorder.h \
 .pio/libdeps/esp32dev/FastLED/src/crgb.hpp \
 .pio/libdeps/esp32dev/FastLED/src/fl/str.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/geometry.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/clamp.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map_range.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math_macros.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/scoped_ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/allocator.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/inplacenew.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/unused.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/deprecated.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/vector.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/functional.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/insert_result.h \
 .pio/libdeps/esp32dev/FastLED/src/pixel_controller.h \
 .pio/libdeps/esp32dev/FastLED/src/rgbw.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/five_bit_hd_gamma.h \
 .pio/libdeps/esp32dev/FastLED/src/dither_mode.h \
 .pio/libdeps/esp32dev/FastLED/src/pixel_iterator.h \
 .pio/libdeps/esp32dev/FastLED/src/cled_controller.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/engine_events.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/screenmap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/lut.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/assert.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/assert_defs.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/strstream.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/warn.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/dbg.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/esp_assert.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/pair.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/singleton.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xymap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xmap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/virtual_if_not_avr.h \
 .pio/libdeps/esp32dev/FastLED/src/fastpin.h \
 .pio/libdeps/esp32dev/FastLED/src/fastspi_types.h \
 .pio/libdeps/esp32dev/FastLED/src/dmx.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/fastled_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/fastpin_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/esp_version.h \
 .pio/libdeps/esp32dev/FastLED/src/third_party/espressif/led_strip/src/enabled.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/clockless_rmt_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/rmt_4/idf4_clockless_rmt_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/rmt_4/idf4_rmt.h \
 .pio/libdeps/esp32dev/FastLED/src/fastled_progmem.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/null_progmem.h \
 .pio/libdeps/esp32dev/FastLED/src/hsv2rgb.h \
 .pio/libdeps/esp32dev/FastLED/src/colorutils.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/colorutils.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/blur.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/colorutils_misc.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/fill.h \
 .pio/libdeps/esp32dev/FastLED/src/pixelset.h \
 .pio/libdeps/esp32dev/FastLED/src/colorpalettes.h \
 .pio/libdeps/esp32dev/FastLED/src/noise.h \
 .pio/libdeps/esp32dev/FastLED/src/power_mgt.h \
 .pio/libdeps/esp32dev/FastLED/src/fastspi.h \
 .pio/libdeps/esp32dev/FastLED/src/fastspi_bitbang.h \
 .pio/libdeps/esp32dev/FastLED/src/chipsets.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/leds.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/function.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/clear.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/stdint.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ui.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/audio.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/fft.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/slice.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/function_list.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ui_impl.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/ui_defs.h \
 .pio/libdeps/esp32dev/FastLED/src/sensors/button.h \
 .pio/libdeps/esp32dev/FastLED/src/sensors/digital_pin.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/sketch_macros.h
