.pio/build/esp32dev/src/wifi_manager.cpp.o: src/wifi_manager.cpp \
 src/wifi_manager.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Arduino.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp_arduino_version.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/FreeRTOS.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_compiler.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/FreeRTOSConfig_arch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/xtensa_config.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa-versions.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core-isa.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core-matmap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/tie.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/system.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/corebits.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime-frames.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp_rom_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/reset_reasons.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp32/rom/ets_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/projdefs.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/portable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/deprecated_definitions.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portmacro.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/specreg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime-core-state.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xt_instr_macros.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/spinlock.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/cpu_hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_err.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_compiler.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/soc_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/cpu_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/esp32/include/hal/cpu_ll.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_attr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/extreg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_bit_defs.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/compare_set.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/include/soc/soc_memory_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/soc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_assert.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_system/include/esp_private/crosscore_int.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_timer/include/esp_timer.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/newlib/platform_include/esp_newlib.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/heap/include/esp_heap_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/heap/include/multi_heap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_system/include/esp_system.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_idf_version.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_mac.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_chip_info.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_random.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portbenchmark.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_api.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portmacro_deprecated.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/mpu_wrappers.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/task.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/list.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/esp_additions/freertos/task_snapshot.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/semphr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/queue.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/task.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_sleep.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/touch_sensor_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/gpio_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/include/soc/gpio_periph.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/io_mux_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/gpio_struct.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/gpio_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/gpio_sig_map.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/queue.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/event_groups.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/timers.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-log.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log_internal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-matrix.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-uart.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/uart_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/variants/esp32/pins_arduino.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-touch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-dac.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/driver/include/driver/gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_intr_alloc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp32/rom/gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-adc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-spi.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-i2c.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-ledc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-rmt.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-sigmadelta.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-timer.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-bt.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-psram.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-rgb-led.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp8266-compat.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/stdlib_noniso.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/binary.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WCharacter.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WString.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/pgmspace.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Stream.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Print.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Printable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPAddress.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WString.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Printable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Client.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Server.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Udp.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Stream.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPAddress.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/HardwareSerial.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/HWCDC.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/USBCDC.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Esp.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/spi_flash/include/esp_partition.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/spi_flash/include/esp_flash.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/spi_flash_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/esp_flash_err.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/spi_flash/include/esp_spi_flash.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/spi_flash/include/esp_spi_flash_counters.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/esp32/spiram.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/io_pin_remap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Arduino.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFi.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Print.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPv6Address.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFiType.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_wifi/include/esp_wifi_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_wifi/include/esp_private/esp_wifi_types_private.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_interface.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_event/include/esp_event_base.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFiSTA.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFiGeneric.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_event/include/esp_event.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_event/include/esp_event_base.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_event/include/esp_event_legacy.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_netif/include/esp_netif.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_netif/include/esp_netif_ip_addr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_netif/include/esp_netif_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_netif/include/esp_netif_defaults.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_eth/include/esp_eth_netif_glue.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_eth/include/esp_eth.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_eth/include/esp_eth_com.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/eth_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_eth/include/esp_eth_mac.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_eth/include/esp_eth_phy.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/tcpip_adapter/include/tcpip_adapter.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/tcpip_adapter/include/tcpip_adapter_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/ip_addr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/opt.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/port/esp32/include/lwipopts.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/newlib/platform_include/sys/ioctl.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_system/include/esp_task.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/port/esp32/include/netif/dhcp_state.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/port/esp32/include/sntp/sntp_get_set_time.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/debug.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/arch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/port/esp32/include/arch/cc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/port/esp32/include/arch/sys_arch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/port/esp32/include/arch/vfs_lwip.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/def.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/ip4_addr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/ip6_addr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/def.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/ip6_zone.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/include/apps/dhcpserver/dhcpserver.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/lwip/lwip/src/include/lwip/err.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_netif/include/esp_netif_sta_list.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_wifi/include/esp_smartconfig.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/wifi_provisioning/include/wifi_provisioning/manager.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/protocomm/include/common/protocomm.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/protocomm/include/security/protocomm_security.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/wifi_provisioning/include/wifi_provisioning/wifi_config.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFiAP.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFiScan.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFiClient.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Client.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFiServer.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Server.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src/WiFiUdp.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Udp.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/cbuf.h \
 src/config.h src/hardware.h .pio/libdeps/esp32dev/FastLED/src/FastLED.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/force_inline.h \
 .pio/libdeps/esp32dev/FastLED/src/cpp_compat.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/register.h \
 .pio/libdeps/esp32dev/FastLED/src/fastled_config.h \
 .pio/libdeps/esp32dev/FastLED/src/led_sysdefs.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/led_sysdefs_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/namespace.h \
 .pio/libdeps/esp32dev/FastLED/src/fastled_delay.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/types.h \
 .pio/libdeps/esp32dev/FastLED/src/bitswap.h \
 .pio/libdeps/esp32dev/FastLED/src/controller.h \
 .pio/libdeps/esp32dev/FastLED/src/cpixel_ledcontroller.h \
 .pio/libdeps/esp32dev/FastLED/src/pixeltypes.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/types.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/lib8static.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/qfx.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/memmove.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/math8.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/scale8.h \
 .pio/libdeps/esp32dev/FastLED/src/crgb.h \
 .pio/libdeps/esp32dev/FastLED/src/chsv.h \
 .pio/libdeps/esp32dev/FastLED/src/color.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/template_magic.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/type_traits.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/intmap.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/random8.h \
 .pio/libdeps/esp32dev/FastLED/src/lib8tion/trig8.h \
 .pio/libdeps/esp32dev/FastLED/src/eorder.h \
 .pio/libdeps/esp32dev/FastLED/src/crgb.hpp \
 .pio/libdeps/esp32dev/FastLED/src/fl/str.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/geometry.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/clamp.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map_range.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/math_macros.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/scoped_ptr.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/allocator.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/inplacenew.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/unused.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/deprecated.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/vector.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/functional.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/insert_result.h \
 .pio/libdeps/esp32dev/FastLED/src/pixel_controller.h \
 .pio/libdeps/esp32dev/FastLED/src/rgbw.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/five_bit_hd_gamma.h \
 .pio/libdeps/esp32dev/FastLED/src/dither_mode.h \
 .pio/libdeps/esp32dev/FastLED/src/pixel_iterator.h \
 .pio/libdeps/esp32dev/FastLED/src/cled_controller.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/engine_events.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/screenmap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/lut.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/map.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/assert.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/assert_defs.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/strstream.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/warn.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/dbg.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/esp_assert.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/pair.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/singleton.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xymap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/xmap.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/virtual_if_not_avr.h \
 .pio/libdeps/esp32dev/FastLED/src/fastpin.h \
 .pio/libdeps/esp32dev/FastLED/src/fastspi_types.h \
 .pio/libdeps/esp32dev/FastLED/src/dmx.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/fastled_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/fastpin_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/esp_version.h \
 .pio/libdeps/esp32dev/FastLED/src/third_party/espressif/led_strip/src/enabled.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/clockless_rmt_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/rmt_4/idf4_clockless_rmt_esp32.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/esp/32/rmt_4/idf4_rmt.h \
 .pio/libdeps/esp32dev/FastLED/src/fastled_progmem.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/null_progmem.h \
 .pio/libdeps/esp32dev/FastLED/src/hsv2rgb.h \
 .pio/libdeps/esp32dev/FastLED/src/colorutils.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/colorutils.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/blur.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/colorutils_misc.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/fill.h \
 .pio/libdeps/esp32dev/FastLED/src/pixelset.h \
 .pio/libdeps/esp32dev/FastLED/src/colorpalettes.h \
 .pio/libdeps/esp32dev/FastLED/src/noise.h \
 .pio/libdeps/esp32dev/FastLED/src/power_mgt.h \
 .pio/libdeps/esp32dev/FastLED/src/fastspi.h \
 .pio/libdeps/esp32dev/FastLED/src/fastspi_bitbang.h \
 .pio/libdeps/esp32dev/FastLED/src/chipsets.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/leds.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/function.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/clear.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/stdint.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ui.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/audio.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/fft.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/slice.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/function_list.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/ui_impl.h \
 .pio/libdeps/esp32dev/FastLED/src/platforms/ui_defs.h \
 .pio/libdeps/esp32dev/FastLED/src/sensors/button.h \
 .pio/libdeps/esp32dev/FastLED/src/sensors/digital_pin.h \
 .pio/libdeps/esp32dev/FastLED/src/fl/sketch_macros.h
