#pragma once

#include "fastled_progmem.h"
#include "fl/namespace.h"

// font courtesy of https://github.com/idispatch/raster-fonts
// copied from the WLED project

FASTLED_NAMESPACE_BEGIN

static const unsigned char console_font_4x6[] FL_PROGMEM = {

// code points 0-31 and 127-255 are commented out to save memory, they contain extra characters (CP437),
// which could be used with an UTF-8 to CP437 conversion

    // /*
    //  * code=0, hex=0x00, ascii="^@"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=1, hex=0x01, ascii="^A"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=2, hex=0x02, ascii="^B"
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=3, hex=0x03, ascii="^C"
    //  */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=4, hex=0x04, ascii="^D"
    //  */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=5, hex=0x05, ascii="^E"
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=6, hex=0x06, ascii="^F"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=7, hex=0x07, ascii="^G"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=8, hex=0x08, ascii="^H"
    //  */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0xD0,  /* 1101 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */

    // /*
    //  * code=9, hex=0x09, ascii="^I"
    //  */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=10, hex=0x0A, ascii="^J"
    //  */
    // 0xF0,  /* 1111 */
    // 0x80,  /* 1000 */
    // 0xA0,  /* 1010 */
    // 0x80,  /* 1000 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */

    // /*
    //  * code=11, hex=0x0B, ascii="^K"
    //  */
    // 0x00,  /* 0000 */
    // 0x30,  /* 0011 */
    // 0x10,  /* 0001 */
    // 0x60,  /* 0110 */
    // 0x60,  /* 0110 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=12, hex=0x0C, ascii="^L"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=13, hex=0x0D, ascii="^M"
    //  */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x60,  /* 0110 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=14, hex=0x0E, ascii="^N"
    //  */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x50,  /* 0101 */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=15, hex=0x0F, ascii="^O"
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=16, hex=0x10, ascii="^P"
    //  */
    // 0x40,  /* 0100 */
    // 0x60,  /* 0110 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x40,  /* 0100 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=17, hex=0x11, ascii="^Q"
    //  */
    // 0x10,  /* 0001 */
    // 0x30,  /* 0011 */
    // 0x70,  /* 0111 */
    // 0x30,  /* 0011 */
    // 0x10,  /* 0001 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=18, hex=0x12, ascii="^R"
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=19, hex=0x13, ascii="^S"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=20, hex=0x14, ascii="^T"
    //  */
    // 0x70,  /* 0111 */
    // 0xD0,  /* 1101 */
    // 0xD0,  /* 1101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=21, hex=0x15, ascii="^U"
    //  */
    // 0x30,  /* 0011 */
    // 0x60,  /* 0110 */
    // 0x50,  /* 0101 */
    // 0x30,  /* 0011 */
    // 0x60,  /* 0110 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=22, hex=0x16, ascii="^V"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=23, hex=0x17, ascii="^W"
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */

    // /*
    //  * code=24, hex=0x18, ascii="^X"
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=25, hex=0x19, ascii="^Y"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=26, hex=0x1A, ascii="^Z"
    //  */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0xF0,  /* 1111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=27, hex=0x1B, ascii="^["
    //  */
    // 0x00,  /* 0000 */
    // 0x40,  /* 0100 */
    // 0xF0,  /* 1111 */
    // 0x40,  /* 0100 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=28, hex=0x1C, ascii="^\"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x40,  /* 0100 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=29, hex=0x1D, ascii="^]"
    //  */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=30, hex=0x1E, ascii="^^"
    //  */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=31, hex=0x1F, ascii="^_"
    //  */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    /*
     * code=32, hex=0x20, ascii=" "
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */

    /*
     * code=33, hex=0x21, ascii="!"
     */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=34, hex=0x22, ascii="""
     */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */

    /*
     * code=35, hex=0x23, ascii="#"
     */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=36, hex=0x24, ascii="$"
     */
    0x20,  /* 0010 */
    0x30,  /* 0011 */
    0x60,  /* 0110 */
    0x30,  /* 0011 */
    0x60,  /* 0110 */
    0x20,  /* 0010 */

    /*
     * code=37, hex=0x25, ascii="%"
     */
    0x40,  /* 0100 */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x10,  /* 0001 */
    0x00,  /* 0000 */

    /*
     * code=38, hex=0x26, ascii="&"
     */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x30,  /* 0011 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=39, hex=0x27, ascii="'"
     */
    0x60,  /* 0110 */
    0x40,  /* 0100 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */

    /*
     * code=40, hex=0x28, ascii="("
     */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=41, hex=0x29, ascii=")"
     */
    0x40,  /* 0100 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x00,  /* 0000 */

    /*
     * code=42, hex=0x2A, ascii="*"
     */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x70,  /* 0111 */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=43, hex=0x2B, ascii="+"
     */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x70,  /* 0111 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */

    /*
     * code=44, hex=0x2C, ascii=","
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x60,  /* 0110 */
    0x40,  /* 0100 */

    /*
     * code=45, hex=0x2D, ascii="-"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */

    /*
     * code=46, hex=0x2E, ascii="."
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=47, hex=0x2F, ascii="/"
     */
    0x10,  /* 0001 */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x00,  /* 0000 */

    /*
     * code=48, hex=0x30, ascii="0"
     */
    0x30,  /* 0011 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=49, hex=0x31, ascii="1"
     */
    0x20,  /* 0010 */
    0x60,  /* 0110 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=50, hex=0x32, ascii="2"
     */
    0x60,  /* 0110 */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=51, hex=0x33, ascii="3"
     */
    0x60,  /* 0110 */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x10,  /* 0001 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=52, hex=0x34, ascii="4"
     */
    0x10,  /* 0001 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x10,  /* 0001 */
    0x10,  /* 0001 */
    0x00,  /* 0000 */

    /*
     * code=53, hex=0x35, ascii="5"
     */
    0x70,  /* 0111 */
    0x40,  /* 0100 */
    0x60,  /* 0110 */
    0x10,  /* 0001 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=54, hex=0x36, ascii="6"
     */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=55, hex=0x37, ascii="7"
     */
    0x70,  /* 0111 */
    0x10,  /* 0001 */
    0x30,  /* 0011 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=56, hex=0x38, ascii="8"
     */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=57, hex=0x39, ascii="9"
     */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x30,  /* 0011 */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=58, hex=0x3A, ascii=":"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=59, hex=0x3B, ascii=";"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */
    0x60,  /* 0110 */
    0x40,  /* 0100 */

    /*
     * code=60, hex=0x3C, ascii="<"
     */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x20,  /* 0010 */
    0x10,  /* 0001 */
    0x00,  /* 0000 */

    /*
     * code=61, hex=0x3D, ascii="="
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=62, hex=0x3E, ascii=">"
     */
    0x40,  /* 0100 */
    0x20,  /* 0010 */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x00,  /* 0000 */

    /*
     * code=63, hex=0x3F, ascii="?"
     */
    0x60,  /* 0110 */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=64, hex=0x40, ascii="@"
     */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x40,  /* 0100 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=65, hex=0x41, ascii="A"
     */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=66, hex=0x42, ascii="B"
     */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=67, hex=0x43, ascii="C"
     */
    0x30,  /* 0011 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=68, hex=0x44, ascii="D"
     */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=69, hex=0x45, ascii="E"
     */
    0x70,  /* 0111 */
    0x40,  /* 0100 */
    0x60,  /* 0110 */
    0x40,  /* 0100 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=70, hex=0x46, ascii="F"
     */
    0x70,  /* 0111 */
    0x40,  /* 0100 */
    0x60,  /* 0110 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x00,  /* 0000 */

    /*
     * code=71, hex=0x47, ascii="G"
     */
    0x30,  /* 0011 */
    0x40,  /* 0100 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=72, hex=0x48, ascii="H"
     */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=73, hex=0x49, ascii="I"
     */
    0x70,  /* 0111 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=74, hex=0x4A, ascii="J"
     */
    0x10,  /* 0001 */
    0x10,  /* 0001 */
    0x10,  /* 0001 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=75, hex=0x4B, ascii="K"
     */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=76, hex=0x4C, ascii="L"
     */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=77, hex=0x4D, ascii="M"
     */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=78, hex=0x4E, ascii="N"
     */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=79, hex=0x4F, ascii="O"
     */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=80, hex=0x50, ascii="P"
     */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x00,  /* 0000 */

    /*
     * code=81, hex=0x51, ascii="Q"
     */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=82, hex=0x52, ascii="R"
     */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=83, hex=0x53, ascii="S"
     */
    0x30,  /* 0011 */
    0x40,  /* 0100 */
    0x70,  /* 0111 */
    0x10,  /* 0001 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=84, hex=0x54, ascii="T"
     */
    0x70,  /* 0111 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=85, hex=0x55, ascii="U"
     */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=86, hex=0x56, ascii="V"
     */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=87, hex=0x57, ascii="W"
     */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=88, hex=0x58, ascii="X"
     */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=89, hex=0x59, ascii="Y"
     */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=90, hex=0x5A, ascii="Z"
     */
    0x70,  /* 0111 */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x40,  /* 0100 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=91, hex=0x5B, ascii="["
     */
    0x60,  /* 0110 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=92, hex=0x5C, ascii="\"
     */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x20,  /* 0010 */
    0x10,  /* 0001 */
    0x10,  /* 0001 */
    0x00,  /* 0000 */

    /*
     * code=93, hex=0x5D, ascii="]"
     */
    0x60,  /* 0110 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=94, hex=0x5E, ascii="^"
     */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */

    /*
     * code=95, hex=0x5F, ascii="_"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0xF0,  /* 1111 */

    /*
     * code=96, hex=0x60, ascii="`"
     */
    0x60,  /* 0110 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */

    /*
     * code=97, hex=0x61, ascii="a"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x30,  /* 0011 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=98, hex=0x62, ascii="b"
     */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=99, hex=0x63, ascii="c"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x30,  /* 0011 */
    0x40,  /* 0100 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=100, hex=0x64, ascii="d"
     */
    0x10,  /* 0001 */
    0x10,  /* 0001 */
    0x30,  /* 0011 */
    0x50,  /* 0101 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=101, hex=0x65, ascii="e"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x70,  /* 0111 */
    0x60,  /* 0110 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=102, hex=0x66, ascii="f"
     */
    0x10,  /* 0001 */
    0x20,  /* 0010 */
    0x70,  /* 0111 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=103, hex=0x67, ascii="g"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x10,  /* 0001 */
    0x70,  /* 0111 */

    /*
     * code=104, hex=0x68, ascii="h"
     */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=105, hex=0x69, ascii="i"
     */
    0x20,  /* 0010 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=106, hex=0x6A, ascii="j"
     */
    0x20,  /* 0010 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x60,  /* 0110 */

    /*
     * code=107, hex=0x6B, ascii="k"
     */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=108, hex=0x6C, ascii="l"
     */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=109, hex=0x6D, ascii="m"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x70,  /* 0111 */
    0x70,  /* 0111 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=110, hex=0x6E, ascii="n"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=111, hex=0x6F, ascii="o"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=112, hex=0x70, ascii="p"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x60,  /* 0110 */
    0x50,  /* 0101 */
    0x60,  /* 0110 */
    0x40,  /* 0100 */

    /*
     * code=113, hex=0x71, ascii="q"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x30,  /* 0011 */
    0x50,  /* 0101 */
    0x30,  /* 0011 */
    0x10,  /* 0001 */

    /*
     * code=114, hex=0x72, ascii="r"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x60,  /* 0110 */
    0x40,  /* 0100 */
    0x40,  /* 0100 */
    0x00,  /* 0000 */

    /*
     * code=115, hex=0x73, ascii="s"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x30,  /* 0011 */
    0x20,  /* 0010 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=116, hex=0x74, ascii="t"
     */
    0x00,  /* 0000 */
    0x20,  /* 0010 */
    0x70,  /* 0111 */
    0x20,  /* 0010 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=117, hex=0x75, ascii="u"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=118, hex=0x76, ascii="v"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=119, hex=0x77, ascii="w"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x50,  /* 0101 */
    0x70,  /* 0111 */
    0x70,  /* 0111 */
    0x00,  /* 0000 */

    /*
     * code=120, hex=0x78, ascii="x"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x50,  /* 0101 */
    0x00,  /* 0000 */

    /*
     * code=121, hex=0x79, ascii="y"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x50,  /* 0101 */
    0x50,  /* 0101 */
    0x20,  /* 0010 */
    0x40,  /* 0100 */

    /*
     * code=122, hex=0x7A, ascii="z"
     */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x60,  /* 0110 */
    0x20,  /* 0010 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=123, hex=0x7B, ascii="{"
     */
    0x30,  /* 0011 */
    0x20,  /* 0010 */
    0x60,  /* 0110 */
    0x20,  /* 0010 */
    0x30,  /* 0011 */
    0x00,  /* 0000 */

    /*
     * code=124, hex=0x7C, ascii="|"
     */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x20,  /* 0010 */
    0x00,  /* 0000 */

    /*
     * code=125, hex=0x7D, ascii="}"
     */
    0x60,  /* 0110 */
    0x20,  /* 0010 */
    0x30,  /* 0011 */
    0x20,  /* 0010 */
    0x60,  /* 0110 */
    0x00,  /* 0000 */

    /*
     * code=126, hex=0x7E, ascii="~"
     */
    0x50,  /* 0101 */
    0xA0,  /* 1010 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */
    0x00,  /* 0000 */

    // /*
    //  * code=127, hex=0x7F, ascii="^?"
    //  */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=128, hex=0x80, ascii="!^@"
    //  */
    // 0x30,  /* 0011 */
    // 0x40,  /* 0100 */
    // 0x40,  /* 0100 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x40,  /* 0100 */

    // /*
    //  * code=129, hex=0x81, ascii="!^A"
    //  */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=130, hex=0x82, ascii="!^B"
    //  */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=131, hex=0x83, ascii="!^C"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x30,  /* 0011 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=132, hex=0x84, ascii="!^D"
    //  */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x30,  /* 0011 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=133, hex=0x85, ascii="!^E"
    //  */
    // 0x40,  /* 0100 */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=134, hex=0x86, ascii="!^F"
    //  */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x30,  /* 0011 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=135, hex=0x87, ascii="!^G"
    //  */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x40,  /* 0100 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x60,  /* 0110 */

    // /*
    //  * code=136, hex=0x88, ascii="!^H"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=137, hex=0x89, ascii="!^I"
    //  */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=138, hex=0x8A, ascii="!^J"
    //  */
    // 0x40,  /* 0100 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=139, hex=0x8B, ascii="!^K"
    //  */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=140, hex=0x8C, ascii="!^L"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=141, hex=0x8D, ascii="!^M"
    //  */
    // 0x40,  /* 0100 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=142, hex=0x8E, ascii="!^N"
    //  */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=143, hex=0x8F, ascii="!^O"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=144, hex=0x90, ascii="!^P"
    //  */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=145, hex=0x91, ascii="!^Q"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x30,  /* 0011 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=146, hex=0x92, ascii="!^R"
    //  */
    // 0x30,  /* 0011 */
    // 0x60,  /* 0110 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=147, hex=0x93, ascii="!^S"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=148, hex=0x94, ascii="!^T"
    //  */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=149, hex=0x95, ascii="!^U"
    //  */
    // 0x40,  /* 0100 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=150, hex=0x96, ascii="!^V"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=151, hex=0x97, ascii="!^W"
    //  */
    // 0x40,  /* 0100 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=152, hex=0x98, ascii="!^X"
    //  */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x40,  /* 0100 */

    // /*
    //  * code=153, hex=0x99, ascii="!^Y"
    //  */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=154, hex=0x9A, ascii="!^Z"
    //  */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=155, hex=0x9B, ascii="!^["
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x40,  /* 0100 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=156, hex=0x9C, ascii="!^\"
    //  */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=157, hex=0x9D, ascii="!^]"
    //  */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=158, hex=0x9E, ascii="!^^"
    //  */
    // 0x00,  /* 0000 */
    // 0x60,  /* 0110 */
    // 0x60,  /* 0110 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=159, hex=0x9F, ascii="!^_"
    //  */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x60,  /* 0110 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=160, hex=0xA0, ascii="! "
    //  */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=161, hex=0xA1, ascii="!!"
    //  */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=162, hex=0xA2, ascii="!""
    //  */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=163, hex=0xA3, ascii="!#"
    //  */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=164, hex=0xA4, ascii="!$"
    //  */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=165, hex=0xA5, ascii="!%"
    //  */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=166, hex=0xA6, ascii="!&"
    //  */
    // 0x30,  /* 0011 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=167, hex=0xA7, ascii="!'"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=168, hex=0xA8, ascii="!("
    //  */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x40,  /* 0100 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=169, hex=0xA9, ascii="!)"
    //  */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x40,  /* 0100 */
    // 0x40,  /* 0100 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=170, hex=0xAA, ascii="!*"
    //  */
    // 0x00,  /* 0000 */
    // 0xE0,  /* 1110 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=171, hex=0xAB, ascii="!+"
    //  */
    // 0x40,  /* 0100 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=172, hex=0xAC, ascii="!,"
    //  */
    // 0x40,  /* 0100 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x10,  /* 0001 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=173, hex=0xAD, ascii="!-"
    //  */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=174, hex=0xAE, ascii="!."
    //  */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0xA0,  /* 1010 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=175, hex=0xAF, ascii="!/"
    //  */
    // 0x00,  /* 0000 */
    // 0xA0,  /* 1010 */
    // 0x50,  /* 0101 */
    // 0xA0,  /* 1010 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=176, hex=0xB0, ascii="!0"
    //  */
    // 0x40,  /* 0100 */
    // 0x10,  /* 0001 */
    // 0x40,  /* 0100 */
    // 0x10,  /* 0001 */
    // 0x40,  /* 0100 */
    // 0x10,  /* 0001 */

    // /*
    //  * code=177, hex=0xB1, ascii="!1"
    //  */
    // 0x50,  /* 0101 */
    // 0xA0,  /* 1010 */
    // 0x50,  /* 0101 */
    // 0xA0,  /* 1010 */
    // 0x50,  /* 0101 */
    // 0xA0,  /* 1010 */

    // /*
    //  * code=178, hex=0xB2, ascii="!2"
    //  */
    // 0xB0,  /* 1011 */
    // 0xE0,  /* 1110 */
    // 0xB0,  /* 1011 */
    // 0xE0,  /* 1110 */
    // 0xB0,  /* 1011 */
    // 0xE0,  /* 1110 */

    // /*
    //  * code=179, hex=0xB3, ascii="!3"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=180, hex=0xB4, ascii="!4"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0xE0,  /* 1110 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=181, hex=0xB5, ascii="!5"
    //  */
    // 0x20,  /* 0010 */
    // 0xE0,  /* 1110 */
    // 0x20,  /* 0010 */
    // 0xE0,  /* 1110 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=182, hex=0xB6, ascii="!6"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0xD0,  /* 1101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=183, hex=0xB7, ascii="!7"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=184, hex=0xB8, ascii="!8"
    //  */
    // 0x00,  /* 0000 */
    // 0xE0,  /* 1110 */
    // 0x20,  /* 0010 */
    // 0xE0,  /* 1110 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=185, hex=0xB9, ascii="!9"
    //  */
    // 0x50,  /* 0101 */
    // 0xD0,  /* 1101 */
    // 0x10,  /* 0001 */
    // 0xD0,  /* 1101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=186, hex=0xBA, ascii="!:"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=187, hex=0xBB, ascii="!;"
    //  */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x10,  /* 0001 */
    // 0xD0,  /* 1101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=188, hex=0xBC, ascii="!<"
    //  */
    // 0x50,  /* 0101 */
    // 0xD0,  /* 1101 */
    // 0x10,  /* 0001 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=189, hex=0xBD, ascii="!="
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=190, hex=0xBE, ascii="!>"
    //  */
    // 0x20,  /* 0010 */
    // 0xE0,  /* 1110 */
    // 0x20,  /* 0010 */
    // 0xE0,  /* 1110 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=191, hex=0xBF, ascii="!?"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0xE0,  /* 1110 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=192, hex=0xC0, ascii="!@"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=193, hex=0xC1, ascii="!A"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=194, hex=0xC2, ascii="!B"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=195, hex=0xC3, ascii="!C"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=196, hex=0xC4, ascii="!D"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=197, hex=0xC5, ascii="!E"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0xF0,  /* 1111 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=198, hex=0xC6, ascii="!F"
    //  */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=199, hex=0xC7, ascii="!G"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=200, hex=0xC8, ascii="!H"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x40,  /* 0100 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=201, hex=0xC9, ascii="!I"
    //  */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x40,  /* 0100 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=202, hex=0xCA, ascii="!J"
    //  */
    // 0x50,  /* 0101 */
    // 0xD0,  /* 1101 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=203, hex=0xCB, ascii="!K"
    //  */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0xD0,  /* 1101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=204, hex=0xCC, ascii="!L"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x40,  /* 0100 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=205, hex=0xCD, ascii="!M"
    //  */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=206, hex=0xCE, ascii="!N"
    //  */
    // 0x50,  /* 0101 */
    // 0xD0,  /* 1101 */
    // 0x00,  /* 0000 */
    // 0xD0,  /* 1101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=207, hex=0xCF, ascii="!O"
    //  */
    // 0x20,  /* 0010 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=208, hex=0xD0, ascii="!P"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=209, hex=0xD1, ascii="!Q"
    //  */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=210, hex=0xD2, ascii="!R"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=211, hex=0xD3, ascii="!S"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=212, hex=0xD4, ascii="!T"
    //  */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=213, hex=0xD5, ascii="!U"
    //  */
    // 0x00,  /* 0000 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=214, hex=0xD6, ascii="!V"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=215, hex=0xD7, ascii="!W"
    //  */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0xD0,  /* 1101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */

    // /*
    //  * code=216, hex=0xD8, ascii="!X"
    //  */
    // 0x20,  /* 0010 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=217, hex=0xD9, ascii="!Y"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0xE0,  /* 1110 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=218, hex=0xDA, ascii="!Z"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=219, hex=0xDB, ascii="!["
    //  */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */

    // /*
    //  * code=220, hex=0xDC, ascii="!\"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */

    // /*
    //  * code=221, hex=0xDD, ascii="!]"
    //  */
    // 0xC0,  /* 1100 */
    // 0xC0,  /* 1100 */
    // 0xC0,  /* 1100 */
    // 0xC0,  /* 1100 */
    // 0xC0,  /* 1100 */
    // 0xC0,  /* 1100 */

    // /*
    //  * code=222, hex=0xDE, ascii="!^"
    //  */
    // 0x30,  /* 0011 */
    // 0x30,  /* 0011 */
    // 0x30,  /* 0011 */
    // 0x30,  /* 0011 */
    // 0x30,  /* 0011 */
    // 0x30,  /* 0011 */

    // /*
    //  * code=223, hex=0xDF, ascii="!_"
    //  */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0xF0,  /* 1111 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=224, hex=0xE0, ascii="!`"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x60,  /* 0110 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=225, hex=0xE1, ascii="!a"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x60,  /* 0110 */
    // 0x50,  /* 0101 */
    // 0x60,  /* 0110 */
    // 0x40,  /* 0100 */

    // /*
    //  * code=226, hex=0xE2, ascii="!b"
    //  */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x40,  /* 0100 */
    // 0x40,  /* 0100 */
    // 0x40,  /* 0100 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=227, hex=0xE3, ascii="!c"
    //  */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=228, hex=0xE4, ascii="!d"
    //  */
    // 0x70,  /* 0111 */
    // 0x40,  /* 0100 */
    // 0x20,  /* 0010 */
    // 0x40,  /* 0100 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=229, hex=0xE5, ascii="!e"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x30,  /* 0011 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=230, hex=0xE6, ascii="!f"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x40,  /* 0100 */

    // /*
    //  * code=231, hex=0xE7, ascii="!g"
    //  */
    // 0x00,  /* 0000 */
    // 0x10,  /* 0001 */
    // 0x60,  /* 0110 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=232, hex=0xE8, ascii="!h"
    //  */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=233, hex=0xE9, ascii="!i"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=234, hex=0xEA, ascii="!j"
    //  */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=235, hex=0xEB, ascii="!k"
    //  */
    // 0x30,  /* 0011 */
    // 0x40,  /* 0100 */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=236, hex=0xEC, ascii="!l"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=237, hex=0xED, ascii="!m"
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=238, hex=0xEE, ascii="!n"
    //  */
    // 0x30,  /* 0011 */
    // 0x40,  /* 0100 */
    // 0x70,  /* 0111 */
    // 0x40,  /* 0100 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=239, hex=0xEF, ascii="!o"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=240, hex=0xF0, ascii="!p"
    //  */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=241, hex=0xF1, ascii="!q"
    //  */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=242, hex=0xF2, ascii="!r"
    //  */
    // 0x60,  /* 0110 */
    // 0x10,  /* 0001 */
    // 0x60,  /* 0110 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=243, hex=0xF3, ascii="!s"
    //  */
    // 0x30,  /* 0011 */
    // 0x40,  /* 0100 */
    // 0x30,  /* 0011 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=244, hex=0xF4, ascii="!t"
    //  */
    // 0x00,  /* 0000 */
    // 0x10,  /* 0001 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */

    // /*
    //  * code=245, hex=0xF5, ascii="!u"
    //  */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x40,  /* 0100 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=246, hex=0xF6, ascii="!v"
    //  */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x70,  /* 0111 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=247, hex=0xF7, ascii="!w"
    //  */
    // 0x00,  /* 0000 */
    // 0x50,  /* 0101 */
    // 0xA0,  /* 1010 */
    // 0x50,  /* 0101 */
    // 0xA0,  /* 1010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=248, hex=0xF8, ascii="!x"
    //  */
    // 0x20,  /* 0010 */
    // 0x50,  /* 0101 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=249, hex=0xF9, ascii="!y"
    //  */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x70,  /* 0111 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=250, hex=0xFA, ascii="!z"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=251, hex=0xFB, ascii="!{"
    //  */
    // 0x30,  /* 0011 */
    // 0x20,  /* 0010 */
    // 0x20,  /* 0010 */
    // 0x60,  /* 0110 */
    // 0x20,  /* 0010 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=252, hex=0xFC, ascii="!|"
    //  */
    // 0x70,  /* 0111 */
    // 0x50,  /* 0101 */
    // 0x50,  /* 0101 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=253, hex=0xFD, ascii="!}"
    //  */
    // 0x60,  /* 0110 */
    // 0x20,  /* 0010 */
    // 0x40,  /* 0100 */
    // 0x60,  /* 0110 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=254, hex=0xFE, ascii="!~"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x60,  /* 0110 */
    // 0x60,  /* 0110 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */

    // /*
    //  * code=255, hex=0xFF, ascii="!^"
    //  */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
    // 0x00,  /* 0000 */
};

FASTLED_NAMESPACE_END
