
/*
<PERSON> line simplification algorithm.
*/

#include "fl/line_simplification.h"

namespace fl {

namespace /*compiled_test*/ {

// // LineSimplifier<float>::LineSimplifier() : epsilon(0.0) {}
// using LineSimplifierF = LineSimplifier<float>;
// using LineSimplifierD = LineSimplifier<double>;

// LineSimplifierF s_test;
// LineSimplifierD s_testd;

// void foo() {
//     fl::vector<vec2<float>> points;
//     s_test.simplifyInplace(&points);
// }

} // namespace

} // namespace fl
