#include "storage.h"
#include "alarm.h"

// === 存储对象定义 ===
Preferences preferences;

void initStorage() {
  preferences.begin("clock", false); // "clock"是命名空间
  loadAlarmSettings();
  Serial.println("✓ 数据存储初始化完成");
}

void saveAlarmSettings() {
  Serial.println("保存闹钟设置到存储器...");
  
  for (int i = 0; i < ALARM_COUNT; i++) {
    String hourKey = "alarm" + String(i) + "_hour";
    String minuteKey = "alarm" + String(i) + "_minute";
    String enabledKey = "alarm" + String(i) + "_enabled";
    
    preferences.putInt(hourKey.c_str(), alarms[i].hour);
    preferences.putInt(minuteKey.c_str(), alarms[i].minute);
    preferences.putBool(enabledKey.c_str(), alarms[i].enabled);
  }
  
  Serial.println("✓ 闹钟设置保存完成");
}

void loadAlarmSettings() {
  Serial.println("从存储器加载闹钟设置...");
  
  for (int i = 0; i < ALARM_COUNT; i++) {
    String hourKey = "alarm" + String(i) + "_hour";
    String minuteKey = "alarm" + String(i) + "_minute";
    String enabledKey = "alarm" + String(i) + "_enabled";
    
    // 加载设置，如果不存在则使用默认值
    alarms[i].hour = preferences.getInt(hourKey.c_str(), alarms[i].hour);
    alarms[i].minute = preferences.getInt(minuteKey.c_str(), alarms[i].minute);
    alarms[i].enabled = preferences.getBool(enabledKey.c_str(), alarms[i].enabled);
    
    Serial.print("闹钟 ");
    Serial.print(i + 1);
    Serial.print(": ");
    if (alarms[i].hour < 10) Serial.print("0");
    Serial.print(alarms[i].hour);
    Serial.print(":");
    if (alarms[i].minute < 10) Serial.print("0");
    Serial.print(alarms[i].minute);
    Serial.print(" (");
    Serial.print(alarms[i].enabled ? "开启" : "关闭");
    Serial.println(")");
  }
}

void resetAlarmSettings() {
  Serial.println("重置闹钟设置为默认值...");
  
  // 恢复默认设置
  alarms[0] = {7, 0, false, false};   // 闹钟1: 07:00, 禁用
  alarms[1] = {12, 0, false, false};  // 闹钟2: 12:00, 禁用
  alarms[2] = {18, 0, false, false};  // 闹钟3: 18:00, 禁用
  
  // 清除存储的设置
  preferences.clear();
  
  // 保存默认设置
  saveAlarmSettings();
  
  Serial.println("✓ 闹钟设置已重置");
}
