#ifndef GRAPHICS_H
#define GRAPHICS_H

#include <Arduino.h>
#include <GxEPD2_BW.h>
#include "config.h"

// === 图标尺寸定义 ===
#define ICON_SIZE_SMALL 16
#define ICON_SIZE_MEDIUM 24
#define ICON_SIZE_LARGE 32

// === 图形绘制函数 ===
void drawWiFiIcon(int x, int y, int strength, int size = ICON_SIZE_MEDIUM);
void drawBatteryIcon(int x, int y, int percentage, int size = ICON_SIZE_MEDIUM);
void drawClockIcon(int x, int y, int hour, int minute, int size = ICON_SIZE_LARGE);
void drawWeatherIcon(int x, int y, String weather, int size = ICON_SIZE_MEDIUM);
void drawAlarmIcon(int x, int y, bool enabled, int size = ICON_SIZE_SMALL);

// === 进度条和图形 ===
void drawProgressBar(int x, int y, int width, int height, int percentage);
void drawCircularProgress(int centerX, int centerY, int radius, int percentage);
void drawSignalBars(int x, int y, int strength);

// === 装饰图形 ===
void drawFrame(int x, int y, int width, int height, int thickness = 1);
void drawRoundedFrame(int x, int y, int width, int height, int radius);
void drawSeparatorLine(int x1, int y1, int x2, int y2);

// === 动画效果 ===
void drawLoadingAnimation(int x, int y, int frame);
void drawPulseEffect(int x, int y, int size, int intensity);

// === 文字效果 ===
void drawShadowText(int x, int y, String text, int shadowOffset = 2);
void drawOutlineText(int x, int y, String text);

// === 布局助手 ===
struct DisplayLayout {
  int timeX, timeY;
  int dateX, dateY;
  int statusX, statusY;
  int iconX, iconY;
};

DisplayLayout calculateLayout();

#endif // GRAPHICS_H
