#include "display.h"
#include "wifi_manager.h"
#include "alarm.h"

// === 显示对象定义 ===
GxEPD2_BW<GxEPD2_290_T94, GxEPD2_290_T94::HEIGHT> display(GxEPD2_290_T94(EPD_CS, EPD_DC, EPD_RST, EPD_BUSY));

// === 显示状态变量定义 ===
int displayMode = DISPLAY_TIME;
bool settingMode = false;
int settingItem = 0;

void initDisplay() {
  display.init();
  display.setRotation(1);
  Serial.println("✓ 电子墨水屏初始化完成");
  
  // 显示启动信息
  showMessage("ESP32 时钟", "初始化中...", "请稍候");
}

void updateClockDisplay() {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    
    // 绘制边框
    display.drawRect(2, 2, display.width()-4, display.height()-4, GxEPD_BLACK);
    
    // 显示时间 (大字体，居中对齐)
    display.setFont(&FreeMonoBold12pt7b);
    display.setCursor(30, 35);
    if (currentHour < 10) display.print("0");
    display.print(currentHour);
    display.print(":");
    if (currentMinute < 10) display.print("0");
    display.print(currentMinute);
    display.print(":");
    if (currentSecond < 10) display.print("0");
    display.print(currentSecond);
    
    // 显示日期 (与时间对齐)
    display.setCursor(30, 65);
    display.print(currentYear);
    display.print("/");
    if (currentMonth < 10) display.print("0");
    display.print(currentMonth);
    display.print("/");
    if (currentDay < 10) display.print("0");
    display.print(currentDay);
    
    // 分割线
    display.drawLine(8, 75, display.width()-8, 75, GxEPD_BLACK);
    
    // 显示WiFi和存储状态
    display.setCursor(8, 100);
    if (wifiConnected) {
      display.print("WiFi:");
      display.print(timeSync ? "同步" : "未同步");
    } else {
      display.print("WiFi:未连接");
    }
    
    // 显示闹钟状态
    display.setCursor(8, 120);
    int enabledCount = getEnabledAlarmCount();
    display.print("闹钟:");
    display.print(enabledCount);
    display.print("/3开启");
    
    // 显示按键提示（如果在设置模式）
    if (settingMode) {
      display.setCursor(8, 140);
      if (settingItem == 0) display.print("[调整小时]");
      else if (settingItem == 1) display.print("[调整分钟]");
      else display.print("[切换项目]");
    }
    
  } while (display.nextPage());
}

void updateAlarmDisplay() {
  if (displayMode == DISPLAY_TIME) {
    updateClockDisplay();
    return;
  }
  
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.drawRect(2, 2, display.width()-4, display.height()-4, GxEPD_BLACK);
    
    // 闹钟设置显示
    int alarmIdx = displayMode - 1;
    display.setFont(&FreeMonoBold12pt7b);
    
    display.setCursor(20, 35);
    display.print("闹钟 ");
    display.print(alarmIdx + 1);
    
    display.setCursor(30, 65);
    if (alarms[alarmIdx].hour < 10) display.print("0");
    display.print(alarms[alarmIdx].hour);
    display.print(":");
    if (alarms[alarmIdx].minute < 10) display.print("0");
    display.print(alarms[alarmIdx].minute);
    
    display.setCursor(8, 95);
    display.print("状态: ");
    display.print(alarms[alarmIdx].enabled ? "开启" : "关闭");
    
    display.setCursor(8, 120);
    if (settingMode) {
      display.print("A:项目 B:+ C:-");
    } else {
      display.print("A:切换 B:开关 C:设置");
    }
    
  } while (display.nextPage());
}

void updateAlarmRingingDisplay() {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.drawRect(2, 2, display.width()-4, display.height()-4, GxEPD_BLACK);
    
    // 闹钟响铃显示
    display.setFont(&FreeMonoBold12pt7b);
    display.setCursor(20, 40);
    display.print("闹钟响铃!");
    
    display.setCursor(30, 70);
    display.print("闹钟 ");
    display.print(currentAlarm + 1);
    
    display.setCursor(15, 100);
    display.print("按任意键停止");
    
  } while (display.nextPage());
}

void showMessage(String line1, String line2, String line3) {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.setFont(&FreeMonoBold12pt7b);
    
    // 第一行
    display.setCursor(10, 40);
    display.print(line1);
    
    // 第二行
    if (line2.length() > 0) {
      display.setCursor(10, 80);
      display.print(line2);
    }
    
    // 第三行
    if (line3.length() > 0) {
      display.setCursor(10, 120);
      display.print(line3);
    }
    
  } while (display.nextPage());
}

void switchDisplayMode() {
  displayMode = (displayMode + 1) % 4; // 0=时间, 1-3=闹钟1-3
  if (displayMode == DISPLAY_TIME) {
    updateClockDisplay();
  } else {
    updateAlarmDisplay();
  }
}

void enterSettingMode() {
  settingMode = true;
  settingItem = 0;
  updateAlarmDisplay();
}

void exitSettingMode() {
  settingMode = false;
  displayMode = DISPLAY_TIME;
  updateClockDisplay();
}

// === 高级时钟显示 ===
void updateAdvancedClockDisplay() {
  display.setRotation(1);
  display.fillScreen(GxEPD_WHITE);

  // 获取布局
  DisplayLayout layout = calculateLayout();

  // 绘制装饰边框
  drawFrame(5, 5, display.width() - 10, display.height() - 10, 2);

  // 绘制时间 (大字体)
  display.setFont(&FreeMonoBold12pt7b);
  display.setTextColor(GxEPD_BLACK);

  String timeStr = String(currentHour) + ":" +
                   (currentMinute < 10 ? String("0") : String("")) + String(currentMinute);
  display.setCursor(layout.timeX, layout.timeY);
  display.print(timeStr);

  // 绘制秒数 (小字体)
  display.setFont();
  display.setCursor(layout.timeX + 120, layout.timeY - 10);
  String secondStr = ":" + (currentSecond < 10 ? String("0") : String("")) + String(currentSecond);
  display.print(secondStr);

  // 绘制日期
  display.setCursor(layout.dateX, layout.dateY);
  String dateStr = String(currentYear) + "/" +
                   (currentMonth < 10 ? String("0") : String("")) + String(currentMonth) + "/" +
                   (currentDay < 10 ? String("0") : String("")) + String(currentDay);
  display.print(dateStr);

  // 绘制状态图标
  drawStatusIcons();

  // 绘制模拟时钟
  drawClockIcon(layout.iconX, layout.iconY, currentHour, currentMinute, ICON_SIZE_LARGE);

  // 绘制分割线
  drawSeparatorLine(10, 80, display.width() - 10, 80);

  // 绘制闹钟状态
  int alarmY = 90;
  for (int i = 0; i < 3; i++) {
    drawAlarmIcon(15 + i * 40, alarmY, alarms[i].enabled, ICON_SIZE_SMALL);
    display.setCursor(15 + i * 40, alarmY + 25);
    display.print(String(alarms[i].hour) + ":" +
                  (alarms[i].minute < 10 ? String("0") : String("")) + String(alarms[i].minute));
  }

  display.display();
}

// === 系统状态显示 ===
void showSystemStatus() {
  display.setRotation(1);
  display.fillScreen(GxEPD_WHITE);

  // 标题
  display.setFont(&FreeMonoBold12pt7b);
  display.setCursor(20, 25);
  display.print("System Status");

  // 重置字体
  display.setFont();

  // WiFi状态
  int y = 40;
  display.setCursor(20, y);
  display.print("WiFi:");
  drawWiFiIcon(80, y - 8, wifiConnected ? 4 : 0, ICON_SIZE_SMALL);
  display.setCursor(110, y);
  display.print(wifiConnected ? "Connected" : "Disconnected");

  // 时间同步状态
  y += 20;
  display.setCursor(20, y);
  display.print("Time Sync:");
  display.setCursor(110, y);
  display.print(timeSync ? "Synced" : "Not synced");

  // 内存使用
  y += 20;
  display.setCursor(20, y);
  display.print("Free Memory:");
  display.setCursor(110, y);
  display.print(String(ESP.getFreeHeap() / 1024) + " KB");

  // 内存进度条
  int memoryPercent = (ESP.getFreeHeap() * 100) / (320 * 1024); // 假设总内存320KB
  drawProgressBar(20, y + 10, 200, 8, memoryPercent);

  // 运行时间
  y += 35;
  display.setCursor(20, y);
  display.print("Uptime:");
  unsigned long uptime = millis() / 1000;
  int hours = uptime / 3600;
  int minutes = (uptime % 3600) / 60;
  display.setCursor(110, y);
  display.print(String(hours) + "h " + String(minutes) + "m");

  display.display();
}

// === 状态图标绘制 ===
void drawStatusIcons() {
  int iconY = 10;

  // WiFi图标
  int wifiStrength = wifiConnected ? 4 : 0;
  drawWiFiIcon(display.width() - 120, iconY, wifiStrength, ICON_SIZE_SMALL);

  // 电池图标 (模拟电量)
  int batteryLevel = 85; // 可以后续连接真实电池检测
  drawBatteryIcon(display.width() - 90, iconY, batteryLevel, ICON_SIZE_SMALL);

  // 时间同步图标
  if (timeSync) {
    display.setCursor(display.width() - 60, iconY + 8);
    display.print("SYNC");
  }
}

// === 天气显示 (示例) ===
void showWeatherDisplay() {
  display.setRotation(1);
  display.fillScreen(GxEPD_WHITE);

  // 标题
  display.setFont(&FreeMonoBold12pt7b);
  display.setCursor(20, 25);
  display.print("Weather");

  display.setFont();

  // 天气图标 (示例)
  drawWeatherIcon(50, 40, "sunny", ICON_SIZE_LARGE);

  // 温度 (示例数据)
  display.setCursor(100, 60);
  display.print("25°C");

  // 湿度
  display.setCursor(100, 80);
  display.print("Humidity: 60%");

  // 湿度进度条
  drawProgressBar(100, 85, 100, 6, 60);

  display.display();
}

// === 动画时钟 ===
void showAnimatedClock() {
  static int animFrame = 0;

  display.setRotation(1);
  display.fillScreen(GxEPD_WHITE);

  // 大时钟在中心
  int centerX = display.width() / 2;
  int centerY = display.height() / 2;

  drawClockIcon(centerX - 40, centerY - 40, currentHour, currentMinute, 80);

  // 加载动画
  drawLoadingAnimation(centerX - 12, centerY + 50, animFrame);

  animFrame++;
  if (animFrame > 100) animFrame = 0;

  display.display();
}
