#include "display.h"
#include "wifi_manager.h"
#include "alarm.h"

// === 显示对象定义 ===
GxEPD2_BW<GxEPD2_290_T94, GxEPD2_290_T94::HEIGHT> display(GxEPD2_290_T94(EPD_CS, EPD_DC, EPD_RST, EPD_BUSY));

// === 显示状态变量定义 ===
int displayMode = DISPLAY_TIME;
bool settingMode = false;
int settingItem = 0;

void initDisplay() {
  display.init();
  display.setRotation(1);
  Serial.println("✓ 电子墨水屏初始化完成");
  
  // 显示启动信息
  showMessage("ESP32 时钟", "初始化中...", "请稍候");
}

void updateClockDisplay() {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    
    // 绘制边框
    display.drawRect(2, 2, display.width()-4, display.height()-4, GxEPD_BLACK);
    
    // 显示时间 (大字体，居中对齐)
    display.setFont(&FreeMonoBold12pt7b);
    display.setCursor(30, 35);
    if (currentHour < 10) display.print("0");
    display.print(currentHour);
    display.print(":");
    if (currentMinute < 10) display.print("0");
    display.print(currentMinute);
    display.print(":");
    if (currentSecond < 10) display.print("0");
    display.print(currentSecond);
    
    // 显示日期 (与时间对齐)
    display.setCursor(30, 65);
    display.print(currentYear);
    display.print("/");
    if (currentMonth < 10) display.print("0");
    display.print(currentMonth);
    display.print("/");
    if (currentDay < 10) display.print("0");
    display.print(currentDay);
    
    // 分割线
    display.drawLine(8, 75, display.width()-8, 75, GxEPD_BLACK);
    
    // 显示WiFi和存储状态
    display.setCursor(8, 100);
    if (wifiConnected) {
      display.print("WiFi:");
      display.print(timeSync ? "同步" : "未同步");
    } else {
      display.print("WiFi:未连接");
    }
    
    // 显示闹钟状态
    display.setCursor(8, 120);
    int enabledCount = getEnabledAlarmCount();
    display.print("闹钟:");
    display.print(enabledCount);
    display.print("/3开启");
    
    // 显示按键提示（如果在设置模式）
    if (settingMode) {
      display.setCursor(8, 140);
      if (settingItem == 0) display.print("[调整小时]");
      else if (settingItem == 1) display.print("[调整分钟]");
      else display.print("[切换项目]");
    }
    
  } while (display.nextPage());
}

void updateAlarmDisplay() {
  if (displayMode == DISPLAY_TIME) {
    updateClockDisplay();
    return;
  }
  
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.drawRect(2, 2, display.width()-4, display.height()-4, GxEPD_BLACK);
    
    // 闹钟设置显示
    int alarmIdx = displayMode - 1;
    display.setFont(&FreeMonoBold12pt7b);
    
    display.setCursor(20, 35);
    display.print("闹钟 ");
    display.print(alarmIdx + 1);
    
    display.setCursor(30, 65);
    if (alarms[alarmIdx].hour < 10) display.print("0");
    display.print(alarms[alarmIdx].hour);
    display.print(":");
    if (alarms[alarmIdx].minute < 10) display.print("0");
    display.print(alarms[alarmIdx].minute);
    
    display.setCursor(8, 95);
    display.print("状态: ");
    display.print(alarms[alarmIdx].enabled ? "开启" : "关闭");
    
    display.setCursor(8, 120);
    if (settingMode) {
      display.print("A:项目 B:+ C:-");
    } else {
      display.print("A:切换 B:开关 C:设置");
    }
    
  } while (display.nextPage());
}

void updateAlarmRingingDisplay() {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.drawRect(2, 2, display.width()-4, display.height()-4, GxEPD_BLACK);
    
    // 闹钟响铃显示
    display.setFont(&FreeMonoBold12pt7b);
    display.setCursor(20, 40);
    display.print("闹钟响铃!");
    
    display.setCursor(30, 70);
    display.print("闹钟 ");
    display.print(currentAlarm + 1);
    
    display.setCursor(15, 100);
    display.print("按任意键停止");
    
  } while (display.nextPage());
}

void showMessage(String line1, String line2, String line3) {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.setFont(&FreeMonoBold12pt7b);
    
    // 第一行
    display.setCursor(10, 40);
    display.print(line1);
    
    // 第二行
    if (line2.length() > 0) {
      display.setCursor(10, 80);
      display.print(line2);
    }
    
    // 第三行
    if (line3.length() > 0) {
      display.setCursor(10, 120);
      display.print(line3);
    }
    
  } while (display.nextPage());
}

void switchDisplayMode() {
  displayMode = (displayMode + 1) % 4; // 0=时间, 1-3=闹钟1-3
  if (displayMode == DISPLAY_TIME) {
    updateClockDisplay();
  } else {
    updateAlarmDisplay();
  }
}

void enterSettingMode() {
  settingMode = true;
  settingItem = 0;
  updateAlarmDisplay();
}

void exitSettingMode() {
  settingMode = false;
  displayMode = DISPLAY_TIME;
  updateClockDisplay();
}
