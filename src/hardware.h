#ifndef HARDWARE_H
#define HARDWARE_H

#include <Arduino.h>
#include <FastLED.h>
#include "config.h"

// === 硬件对象声明 ===
extern CRGB leds[NUM_LEDS];

// === 硬件初始化函数 ===
void initHardware();
void initLED();
void initButtons();
void initBuzzer();

// === LED 控制函数 ===
void setLED(CRGB color);
void ledOff();
void ledBlink(CRGB color, int duration = 500);

// === 蜂鸣器控制函数 ===
void playTone(int frequency, int duration);
void playStartupSound();
void playAlarmSound();
void stopSound();

// === 按键读取函数 ===
struct ButtonState {
  bool keyA;
  bool keyB;
  bool keyC;
  bool keyA_pressed;
  bool keyB_pressed;
  bool keyC_pressed;
};

ButtonState readButtons();

#endif // HARDWARE_H
