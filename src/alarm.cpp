#include "alarm.h"
#include "hardware.h"
#include "wifi_manager.h"

// === 闹钟变量定义 ===
Alarm alarms[ALARM_COUNT] = {
  {7, 0, false, false},   // 闹钟1: 07:00, 禁用
  {12, 0, false, false},  // 闹钟2: 12:00, 禁用
  {18, 0, false, false}   // 闹钟3: 18:00, 禁用
};

bool alarmRinging = false;
unsigned long alarmStartTime = 0;
int currentAlarm = 0;
bool snoozeMode = false;
unsigned long snoozeTime = 0;

void initAlarms() {
  Serial.println("✓ 闹钟系统初始化完成");
}

void checkAlarms() {
  if (alarmRinging) {
    // 如果正在响铃，播放声音和LED效果
    playAlarmSound();
    
    static unsigned long lastLED = 0;
    static bool ledState = false;
    if (millis() - lastLED >= 500) {
      ledState = !ledState;
      setLED(ledState ? CRGB::Red : CRGB::Yellow);
      lastLED = millis();
    }
    return;
  }
  
  // 检查闹钟触发
  for (int i = 0; i < ALARM_COUNT; i++) {
    if (alarms[i].enabled && 
        alarms[i].hour == currentHour && 
        alarms[i].minute == currentMinute && 
        currentSecond == 0 && 
        !alarms[i].triggered) {
      
      triggerAlarm(i);
      alarms[i].triggered = true;
      break;
    }
    
    // 重置触发状态（当分钟改变时）
    if (alarms[i].minute != currentMinute) {
      alarms[i].triggered = false;
    }
  }
  
  // 检查贪睡
  if (snoozeMode && millis() - snoozeTime >= SNOOZE_TIME_MS) {
    snoozeMode = false;
    triggerAlarm(currentAlarm);
  }
}

void triggerAlarm(int alarmIndex) {
  alarmRinging = true;
  alarmStartTime = millis();
  currentAlarm = alarmIndex;
  
  Serial.print("闹钟 ");
  Serial.print(alarmIndex + 1);
  Serial.println(" 响铃！");
}

void stopAlarm() {
  alarmRinging = false;
  snoozeMode = false;
  
  // 停止声音和LED
  stopSound();
  ledOff();
  
  Serial.println("闹钟已停止");
}

void snoozeAlarm() {
  alarmRinging = false;
  snoozeMode = true;
  snoozeTime = millis();
  
  // 停止声音和LED
  stopSound();
  ledOff();
  
  Serial.println("闹钟贪睡5分钟");
}

int getEnabledAlarmCount() {
  int count = 0;
  for (int i = 0; i < ALARM_COUNT; i++) {
    if (alarms[i].enabled) count++;
  }
  return count;
}
