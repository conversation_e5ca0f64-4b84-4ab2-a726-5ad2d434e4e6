#include "wifi_manager.h"
#include "hardware.h"

// WiFi配置（在config.h中声明，这里定义）
const char* ssid = "Yang";
const char* password = "123456aa..";

// === 状态变量定义 ===
bool wifiConnected = false;
bool timeSync = false;

// === 时间变量定义 ===
int currentHour = 12;
int currentMinute = 0;
int currentSecond = 0;
int currentDay = 1;
int currentMonth = 1;
int currentYear = 2025;

void scanWiFiNetworks() {
  Serial.println("扫描可用WiFi网络...");
  int n = WiFi.scanNetworks();
  Serial.println("扫描完成");

  if (n == 0) {
    Serial.println("没有找到WiFi网络");
  } else {
    Serial.print("找到 ");
    Serial.print(n);
    Serial.println(" 个WiFi网络:");

    for (int i = 0; i < n; ++i) {
      Serial.print(i + 1);
      Serial.print(": ");
      Serial.print(WiFi.SSID(i));
      Serial.print(" (");
      Serial.print(WiFi.RSSI(i));
      Serial.print("dBm) ");
      Serial.println((WiFi.encryptionType(i) == WIFI_AUTH_OPEN) ? "开放" : "加密");
    }
  }
  Serial.println("");
}

void initWiFi() {
  // 先扫描可用网络
  scanWiFiNetworks();

  Serial.print("尝试连接到WiFi: ");
  Serial.println(ssid);

  WiFi.begin(ssid, password);
  Serial.print("连接中");

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    Serial.println();
    Serial.print("✓ WiFi连接成功！IP地址: ");
    Serial.println(WiFi.localIP());
    
    // 连接成功后同步时间
    syncTimeFromNTP();
    
    // LED指示连接成功
    setLED(CRGB::Green);
    playTone(NOTE_C5, 200);
    delay(300);
    
  } else {
    wifiConnected = false;
    Serial.println();
    Serial.println("✗ WiFi连接失败，将使用手动时间");
    
    // LED指示连接失败
    setLED(CRGB::Red);
    playTone(NOTE_C4, 500);
    delay(600);
  }
  
  ledOff();
}

void syncTimeFromNTP() {
  if (!wifiConnected) return;
  
  Serial.println("同步网络时间...");
  configTime(GMT_OFFSET_SEC, DAYLIGHT_OFFSET_SEC, NTP_SERVER);
  
  // 等待时间同步
  int attempts = 0;
  while (!time(nullptr) && attempts < 10) {
    delay(1000);
    Serial.print(".");
    attempts++;
  }
  
  if (time(nullptr)) {
    timeSync = true;
    updateTimeFromNTP();
    Serial.println();
    Serial.println("✓ 时间同步成功！");
    
    // 音效提示同步成功
    playTone(NOTE_E4, 100); delay(150);
    playTone(NOTE_G4, 100); delay(150);
    playTone(NOTE_C5, 200);
  } else {
    timeSync = false;
    Serial.println();
    Serial.println("✗ 时间同步失败");
  }
}

void updateTimeFromNTP() {
  if (!timeSync) return;
  
  time_t now;
  struct tm timeinfo;
  time(&now);
  localtime_r(&now, &timeinfo);
  
  currentHour = timeinfo.tm_hour;
  currentMinute = timeinfo.tm_min;
  currentSecond = timeinfo.tm_sec;
  currentDay = timeinfo.tm_mday;
  currentMonth = timeinfo.tm_mon + 1;
  currentYear = timeinfo.tm_year + 1900;
}

void updateTimeFromMillis() {
  // 简单的时间计算（从启动开始）
  unsigned long totalSeconds = millis() / 1000;
  currentSecond = totalSeconds % 60;
  currentMinute = (totalSeconds / 60) % 60;
  currentHour = ((totalSeconds / 3600) + 12) % 24;
}

void printTimeStatus() {
  Serial.println("=== 系统状态 ===");
  Serial.print("时间: ");
  if (currentHour < 10) Serial.print("0");
  Serial.print(currentHour);
  Serial.print(":");
  if (currentMinute < 10) Serial.print("0");
  Serial.print(currentMinute);
  Serial.print(":");
  if (currentSecond < 10) Serial.print("0");
  Serial.print(currentSecond);
  Serial.print(" 日期: ");
  Serial.print(currentYear);
  Serial.print("/");
  Serial.print(currentMonth);
  Serial.print("/");
  Serial.println(currentDay);
  
  Serial.print("WiFi状态: ");
  Serial.print(wifiConnected ? "已连接" : "未连接");
  if (wifiConnected) {
    Serial.print(" (");
    Serial.print(WiFi.localIP());
    Serial.print(")");
  }
  Serial.println();
  
  Serial.print("时间同步: ");
  Serial.println(timeSync ? "已同步" : "未同步");
  Serial.println("================");
}
