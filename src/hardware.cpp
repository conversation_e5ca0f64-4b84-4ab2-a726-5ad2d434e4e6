#include "hardware.h"

// === 硬件对象定义 ===
CRGB leds[NUM_LEDS];

void initHardware() {
  Serial.println("=== 硬件初始化 ===");
  initLED();
  initButtons();
  initBuzzer();
  Serial.println("✓ 硬件初始化完成");
}

void initLED() {
  FastLED.addLeds<WS2812B, LED_PIN, GRB>(leds, NUM_LEDS);
  FastLED.setBrightness(50);
  ledOff();
  Serial.println("✓ WS2812B LED 初始化完成");
}

void initButtons() {
  pinMode(KEY_A, INPUT_PULLUP);
  pinMode(KEY_B, INPUT_PULLUP);
  pinMode(KEY_C, INPUT_PULLUP);
  Serial.println("✓ 触摸按键初始化完成");
}

void initBuzzer() {
  pinMode(BUZZER_PIN, OUTPUT);
  Serial.println("✓ 蜂鸣器初始化完成");
}

void setLED(CRGB color) {
  leds[0] = color;
  FastLED.show();
}

void ledOff() {
  setLED(CRGB::Black);
}

void ledBlink(CRGB color, int duration) {
  setLED(color);
  delay(duration);
  ledOff();
}

void playTone(int frequency, int duration) {
  tone(BUZZER_PIN, frequency, duration);
}

void playStartupSound() {
  Serial.println("播放启动音效...");
  playTone(NOTE_C4, 200); delay(250);
  playTone(NOTE_E4, 200); delay(250);
  playTone(NOTE_G4, 300); delay(350);
  noTone(BUZZER_PIN);
}

void playAlarmSound() {
  static unsigned long lastSound = 0;
  static bool soundState = false;
  
  if (millis() - lastSound >= 500) {
    soundState = !soundState;
    if (soundState) {
      playTone(NOTE_C5, 200);
    } else {
      playTone(NOTE_E4, 200);
    }
    lastSound = millis();
  }
}

void stopSound() {
  noTone(BUZZER_PIN);
}

ButtonState readButtons() {
  static bool lastKeyA = false, lastKeyB = false, lastKeyC = false;
  
  ButtonState state;
  state.keyA = !digitalRead(KEY_A);
  state.keyB = !digitalRead(KEY_B);
  state.keyC = !digitalRead(KEY_C);
  
  // 检测按键按下（边沿触发）
  state.keyA_pressed = state.keyA && !lastKeyA;
  state.keyB_pressed = state.keyB && !lastKeyB;
  state.keyC_pressed = state.keyC && !lastKeyC;
  
  lastKeyA = state.keyA;
  lastKeyB = state.keyB;
  lastKeyC = state.keyC;
  
  return state;
}
