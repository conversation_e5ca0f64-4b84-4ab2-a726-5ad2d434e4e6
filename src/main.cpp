#include <Arduino.h>
#include <GxEPD2_BW.h>
#include <GxEPD2_3C.h>
#include <SPI.h>
#include <Fonts/FreeMonoBold12pt7b.h>

// 屏幕型号和引脚定义（GDEY029T94, 2.9寸黑白墨水屏）
#define EPD_CS   26   // 片选
#define EPD_DC   27   // 数据/命令
#define EPD_RST  14   // 复位
#define EPD_BUSY 12   // 忙状态

// 屏幕对象
GxEPD2_BW<GxEPD2_290_T94, GxEPD2_290_T94::HEIGHT> display(GxEPD2_290_T94(EPD_CS, EPD_DC, EPD_RST, EPD_BUSY));

void setup() {
  Serial.begin(115200);
  delay(100); // 等待串口

  // 打印启动信息
  Serial.println("=== ESP32 时钟启动 ===");
  Serial.println("硬件信息:");
  Serial.println("- 主控: ESP32-WROOM-32");
  Serial.println("- 显示: 2.9寸电子墨水屏");
  Serial.println("- LED: WS2812B RGB");
  Serial.println("- 按键: 3个触摸按键");
  Serial.println("- 蜂鸣器: 无源蜂鸣器");
  Serial.println("====================");

  display.init(); // 初始化电子墨水屏
  display.setRotation(1); // 横屏竖屏，自己试（可0-3）

  display.setFullWindow(); // 使用全屏刷新
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);   // 白底
    display.setTextColor(GxEPD_BLACK);// 黑字
    display.setFont(&FreeMonoBold12pt7b); // 字体可以改
    display.setCursor(10, 60);         // 文字起点(x, y)
    display.print("Hello, World!");
    display.setCursor(10, 100);        // 第二行文字
    display.print("Learning ESP32!");
  } while (display.nextPage());

  Serial.println("初始化完成！开始主循环...");
}

void loop() {
  // 每秒打印一次运行状态
  static unsigned long lastTime = 0;
  static int counter = 0;

  unsigned long currentTime = millis(); // 获取运行时间（毫秒）

  if (currentTime - lastTime >= 1000) { // 每1000毫秒（1秒）执行一次
    counter++;
    Serial.print("运行时间: ");
    Serial.print(currentTime / 1000);
    Serial.print(" 秒, 循环次数: ");
    Serial.println(counter);

    lastTime = currentTime;
  }
}
