/*
 * ESP32 智能时钟项目
 *
 * 功能特性:
 * - WiFi 网络时间同步
 * - 3个独立闹钟设置
 * - 电子墨水屏显示
 * - RGB LED 状态指示
 * - 触摸按键控制
 * - 蜂鸣器音效反馈
 * - 数据持久化存储
 *
 * 硬件配置:
 * - 主控: ESP32-WROOM-32
 * - 显示: 2.9寸电子墨水屏
 * - LED: WS2812B RGB
 * - 按键: 3个触摸按键
 * - 蜂鸣器: 无源蜂鸣器
 *
 * 作者: ESP32 学习项目
 * 日期: 2025年1月
 */

#include <Arduino.h>
#include "config.h"
#include "hardware.h"
#include "wifi_manager.h"
#include "alarm.h"
#include "display.h"
#include "storage.h"
#include "web_server.h"

// === 系统状态变量 ===
static unsigned long lastSecond = 0;
static unsigned long lastDisplay = 0;
static unsigned long lastButton = 0;
static unsigned long lastDebug = 0;
static unsigned long lastSync = 0;

// === 按键处理函数 ===
void handleButtons();

void setup() {
  Serial.begin(SERIAL_BAUD);
  delay(100);

  Serial.println("=== ESP32 智能时钟启动 ===");

  // 初始化各个模块
  initStorage();      // 数据存储
  initHardware();     // 硬件初始化
  initDisplay();      // 显示初始化
  initAlarms();       // 闹钟系统

  // 播放启动音效
  playStartupSound();

  // WiFi连接和时间同步
  showMessage("WiFi连接中", "请稍候...", "");
  initWiFi();

  // 启动Web服务器
  if (wifiConnected) {
    showMessage("启动Web服务器", "请稍候...", "");
    initWebServer();
  }

  // 显示主界面
  updateClockDisplay();
  Serial.println("✓ 系统启动完成！");
}

void loop() {
  unsigned long currentTime = millis();

  // 每秒更新时间
  if (currentTime - lastSecond >= 1000) {
    if (timeSync) {
      updateTimeFromNTP();
    } else {
      updateTimeFromMillis();
    }
    lastSecond = currentTime;

    // 时间指示LED（每秒闪烁）
    if (!alarmRinging && !settingMode) {
      setLED((currentSecond % 2 == 0) ? CRGB::Blue : CRGB::Black);
    }
  }

  // 每5秒更新屏幕显示
  if (currentTime - lastDisplay >= 5000) {
    if (alarmRinging) {
      updateAlarmRingingDisplay();
    } else if (displayMode == DISPLAY_TIME) {
      updateClockDisplay();
    } else {
      updateAlarmDisplay();
    }
    lastDisplay = currentTime;
  }

  // 每小时重新同步时间
  if (currentTime - lastSync >= 3600000 && wifiConnected) {
    Serial.println("定时重新同步网络时间...");
    syncTimeFromNTP();
    lastSync = currentTime;
  }

  // 每100ms检查按键
  if (currentTime - lastButton >= 100) {
    handleButtons();
    lastButton = currentTime;
  }

  // 检查闹钟
  checkAlarms();

  // 处理Web服务器请求
  if (wifiConnected) {
    handleWebServer();
  }

  // 每10秒打印系统状态
  if (currentTime - lastDebug >= 10000) {
    printTimeStatus();
    lastDebug = currentTime;
  }
}

// === 按键处理函数实现 ===
void handleButtons() {
  ButtonState buttons = readButtons();

  if (alarmRinging) {
    // 闹钟响铃时，任意按键停止闹钟
    if (buttons.keyA_pressed || buttons.keyB_pressed || buttons.keyC_pressed) {
      stopAlarm();
      updateClockDisplay();
    }
    return;
  }

  if (buttons.keyA_pressed) {
    playTone(NOTE_C4, 100);
    if (settingMode) {
      // 设置模式：切换设置项或退出
      settingItem = (settingItem + 1) % 3;
      if (settingItem == 2) {
        exitSettingMode();
        saveAlarmSettings();
      }
    } else {
      // 正常模式：切换显示模式
      switchDisplayMode();
    }
  }

  if (buttons.keyB_pressed) {
    playTone(NOTE_E4, 100);
    if (settingMode && displayMode > 0) {
      // 设置模式：增加数值
      int alarmIdx = displayMode - 1;
      if (settingItem == 0) {
        alarms[alarmIdx].hour = (alarms[alarmIdx].hour + 1) % 24;
      } else if (settingItem == 1) {
        alarms[alarmIdx].minute = (alarms[alarmIdx].minute + 1) % 60;
      }
      updateAlarmDisplay();
    } else if (displayMode > 0) {
      // 正常模式：开关闹钟
      int alarmIdx = displayMode - 1;
      alarms[alarmIdx].enabled = !alarms[alarmIdx].enabled;
      updateAlarmDisplay();
      saveAlarmSettings();
    }
  }

  if (buttons.keyC_pressed) {
    playTone(NOTE_G4, 100);
    if (displayMode > 0 && !settingMode) {
      // 进入设置模式
      enterSettingMode();
    } else if (settingMode && displayMode > 0) {
      // 设置模式：减少数值
      int alarmIdx = displayMode - 1;
      if (settingItem == 0) {
        alarms[alarmIdx].hour = (alarms[alarmIdx].hour - 1 + 24) % 24;
      } else if (settingItem == 1) {
        alarms[alarmIdx].minute = (alarms[alarmIdx].minute - 1 + 60) % 60;
      }
      updateAlarmDisplay();
    }
  }
}

// === 按键处理函数 ===
void handleButtons() {
  ButtonState buttons = readButtons();

  if (alarmRinging) {
    // 闹钟响铃时，任意按键停止闹钟
    if (buttons.keyA_pressed || buttons.keyB_pressed || buttons.keyC_pressed) {
      stopAlarm();
      updateClockDisplay();
    }
    return;
  }

  if (buttons.keyA_pressed) {
    playTone(NOTE_C4, 100);
    if (settingMode) {
      // 设置模式：切换设置项或退出
      settingItem = (settingItem + 1) % 3;
      if (settingItem == 2) {
        exitSettingMode();
        saveAlarmSettings();
      }
    } else {
      // 正常模式：切换显示模式
      switchDisplayMode();
    }
  }

  if (buttons.keyB_pressed) {
    playTone(NOTE_E4, 100);
    if (settingMode && displayMode > 0) {
      // 设置模式：增加数值
      int alarmIdx = displayMode - 1;
      if (settingItem == 0) {
        alarms[alarmIdx].hour = (alarms[alarmIdx].hour + 1) % 24;
      } else if (settingItem == 1) {
        alarms[alarmIdx].minute = (alarms[alarmIdx].minute + 1) % 60;
      }
      updateAlarmDisplay();
    } else if (displayMode > 0) {
      // 正常模式：开关闹钟
      int alarmIdx = displayMode - 1;
      alarms[alarmIdx].enabled = !alarms[alarmIdx].enabled;
      updateAlarmDisplay();
      saveAlarmSettings();
    }
  }

  if (buttons.keyC_pressed) {
    playTone(NOTE_G4, 100);
    if (displayMode > 0 && !settingMode) {
      // 进入设置模式
      enterSettingMode();
    } else if (settingMode && displayMode > 0) {
      // 设置模式：减少数值
      int alarmIdx = displayMode - 1;
      if (settingItem == 0) {
        alarms[alarmIdx].hour = (alarms[alarmIdx].hour - 1 + 24) % 24;
      } else if (settingItem == 1) {
        alarms[alarmIdx].minute = (alarms[alarmIdx].minute - 1 + 60) % 60;
      }
      updateAlarmDisplay();
    }
  }

