#include <Arduino.h>
#include <GxEPD2_BW.h>
#include <GxEPD2_3C.h>
#include <SPI.h>
#include <Fonts/FreeMonoBold12pt7b.h>
#include <FastLED.h>

// === GPIO 引脚定义（根据硬件设计文档） ===
// 电子墨水屏 SPI 接口
#define EPD_CS   26   // 片选
#define EPD_DC   27   // 数据/命令控制
#define EPD_RST  14   // 复位
#define EPD_BUSY 12   // 忙状态检测
#define EPD_SCK  18   // SPI 时钟
#define EPD_MOSI 23   // SPI 数据输出

// WS2812B RGB LED
#define LED_PIN     16  // RGB LED 控制引脚
#define NUM_LEDS    1   // LED 数量（假设只有1个，可根据实际调整）

// 蜂鸣器
#define BUZZER_PIN  25  // 无源蜂鸣器

// 触摸按键
#define KEY_A       33  // 触摸按键 A
#define KEY_B       13  // 触摸按键 B
#define KEY_C       5   // 触摸按键 C

// === 硬件对象初始化 ===
// 电子墨水屏对象
GxEPD2_BW<GxEPD2_290_T94, GxEPD2_290_T94::HEIGHT> display(GxEPD2_290_T94(EPD_CS, EPD_DC, EPD_RST, EPD_BUSY));

// WS2812B LED 数组
CRGB leds[NUM_LEDS];

void setup() {
  Serial.begin(115200);
  delay(100); // 等待串口

  // === 硬件初始化 ===
  Serial.println("=== ESP32 时钟启动 ===");

  // 初始化 WS2812B LED
  FastLED.addLeds<WS2812B, LED_PIN, GRB>(leds, NUM_LEDS);
  FastLED.setBrightness(50); // 设置亮度 (0-255)
  Serial.println("✓ WS2812B LED 初始化完成");

  // 初始化触摸按键 - 尝试不同的模式
  pinMode(KEY_A, INPUT_PULLUP);  // 上拉电阻
  pinMode(KEY_B, INPUT_PULLUP);
  pinMode(KEY_C, INPUT_PULLUP);
  Serial.println("✓ 触摸按键初始化完成（上拉模式）");
  Serial.println("触摸按键测试模式：请尝试触摸按键...");

  // 初始化蜂鸣器引脚
  pinMode(BUZZER_PIN, OUTPUT);
  Serial.println("✓ 蜂鸣器初始化完成");

  // 打印硬件信息
  Serial.println("硬件配置:");
  Serial.println("- 主控: ESP32-WROOM-32");
  Serial.println("- 显示: 2.9寸电子墨水屏");
  Serial.println("- LED: WS2812B RGB (GPIO16)");
  Serial.println("- 按键: 3个触摸按键 (GPIO33,13,5)");
  Serial.println("- 蜂鸣器: 无源蜂鸣器 (GPIO25)");
  Serial.println("====================");

  // 初始化电子墨水屏
  display.init();
  display.setRotation(1); // 横屏显示
  Serial.println("✓ 电子墨水屏初始化完成");

  // 显示欢迎信息
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.setFont(&FreeMonoBold12pt7b);
    display.setCursor(10, 40);
    display.print("ESP32 Clock");
    display.setCursor(10, 80);
    display.print("RGB LED Test");
    display.setCursor(10, 120);
    display.print("Ready!");
  } while (display.nextPage());

  // LED 启动动画
  Serial.println("LED 启动动画...");
  leds[0] = CRGB::Red;   FastLED.show(); delay(300);
  leds[0] = CRGB::Green; FastLED.show(); delay(300);
  leds[0] = CRGB::Blue;  FastLED.show(); delay(300);
  leds[0] = CRGB::Black; FastLED.show(); // 关闭

  Serial.println("✓ 初始化完成！开始主循环...");
}

void loop() {
  static unsigned long lastTime = 0;
  static int counter = 0;
  static int colorIndex = 0;

  unsigned long currentTime = millis();

  // 每2秒切换一次 LED 颜色
  if (currentTime - lastTime >= 2000) {
    counter++;

    // 定义颜色数组
    CRGB colors[] = {CRGB::Red, CRGB::Green, CRGB::Blue, CRGB::Yellow,
                     CRGB::Purple, CRGB::Cyan, CRGB::White, CRGB::Black};
    String colorNames[] = {"红色", "绿色", "蓝色", "黄色",
                          "紫色", "青色", "白色", "关闭"};

    // 设置 LED 颜色
    leds[0] = colors[colorIndex];
    FastLED.show();

    // 打印状态信息
    Serial.print("运行时间: ");
    Serial.print(currentTime / 1000);
    Serial.print(" 秒, 循环: ");
    Serial.print(counter);
    Serial.print(", LED颜色: ");
    Serial.println(colorNames[colorIndex]);

    // 读取按键状态 - 尝试多种检测方法

    // 方法1：数字读取（上拉模式：按下=LOW，释放=HIGH）
    bool keyA_digital = digitalRead(KEY_A);
    bool keyB_digital = digitalRead(KEY_B);
    bool keyC_digital = digitalRead(KEY_C);

    // 方法2：模拟读取
    int analogA = analogRead(KEY_A);
    int analogB = analogRead(KEY_B);
    int analogC = analogRead(KEY_C);

    // 打印详细信息
    Serial.print("数字值 A:");
    Serial.print(keyA_digital);
    Serial.print(" B:");
    Serial.print(keyB_digital);
    Serial.print(" C:");
    Serial.print(keyC_digital);

    Serial.print(" | 模拟值 A:");
    Serial.print(analogA);
    Serial.print(" B:");
    Serial.print(analogB);
    Serial.print(" C:");
    Serial.print(analogC);

    // 检测按键按下（上拉模式：按下时为LOW）
    bool keyA_pressed = !keyA_digital;  // 取反：LOW表示按下
    bool keyB_pressed = !keyB_digital;
    bool keyC_pressed = !keyC_digital;

    if (keyA_pressed || keyB_pressed || keyC_pressed) {
      Serial.print(" >>> 按键按下: ");
      if (keyA_pressed) Serial.print("A ");
      if (keyB_pressed) Serial.print("B ");
      if (keyC_pressed) Serial.print("C ");
    }
    Serial.println();

    // 切换到下一个颜色
    colorIndex = (colorIndex + 1) % 8;
    lastTime = currentTime;
  }
}
