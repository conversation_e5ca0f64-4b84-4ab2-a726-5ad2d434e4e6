#include <Arduino.h>
#include <GxEPD2_BW.h>
#include <GxEPD2_3C.h>
#include <SPI.h>
#include <Fonts/FreeMonoBold12pt7b.h>
#include <FastLED.h>
#include <WiFi.h>
#include <time.h>
#include <Preferences.h>

// === GPIO 引脚定义（根据硬件设计文档） ===
// 电子墨水屏 SPI 接口
#define EPD_CS   26   // 片选
#define EPD_DC   27   // 数据/命令控制
#define EPD_RST  14   // 复位
#define EPD_BUSY 12   // 忙状态检测
#define EPD_SCK  18   // SPI 时钟
#define EPD_MOSI 23   // SPI 数据输出

// WS2812B RGB LED
#define LED_PIN     16  // RGB LED 控制引脚
#define NUM_LEDS    1   // LED 数量（假设只有1个，可根据实际调整）

// 蜂鸣器
#define BUZZER_PIN  25  // 无源蜂鸣器

// 触摸按键
#define KEY_A       33  // 触摸按键 A
#define KEY_B       13  // 触摸按键 B
#define KEY_C       5   // 触摸按键 C

// === 硬件对象初始化 ===
// 电子墨水屏对象
GxEPD2_BW<GxEPD2_290_T94, GxEPD2_290_T94::HEIGHT> display(GxEPD2_290_T94(EPD_CS, EPD_DC, EPD_RST, EPD_BUSY));

// WS2812B LED 数组
CRGB leds[NUM_LEDS];

// === 数据存储对象 ===
Preferences preferences;

// === 音符频率定义 ===
#define NOTE_C4  262   // 中央C (Do)
#define NOTE_D4  294   // D (Re)
#define NOTE_E4  330   // E (Mi)
#define NOTE_F4  349   // F (Fa)
#define NOTE_G4  392   // G (So)
#define NOTE_A4  440   // A (La)
#define NOTE_B4  494   // B (Si)
#define NOTE_C5  523   // 高音C

// === WiFi 配置 ===
const char* ssid = "Yang";        // 请修改为您的WiFi名称
const char* password = "123456aa..";    // 请修改为您的WiFi密码

// NTP 时间服务器配置
const char* ntpServer = "pool.ntp.org";
const long gmtOffset_sec = 8 * 3600;     // 中国时区 GMT+8
const int daylightOffset_sec = 0;        // 夏令时偏移

// === 时钟变量 ===
int currentHour = 12;    // 当前小时 (0-23)
int currentMinute = 0;   // 当前分钟 (0-59)
int currentSecond = 0;   // 当前秒数 (0-59)
int currentDay = 1;      // 当前日期
int currentMonth = 1;    // 当前月份
int currentYear = 2025;  // 当前年份

bool settingMode = false; // 是否在设置模式
int settingItem = 0;     // 设置项目 (0=小时, 1=分钟, 2=日期)
bool wifiConnected = false; // WiFi连接状态
bool timeSync = false;   // 时间同步状态

// === 闹钟功能变量 ===
struct Alarm {
  int hour;           // 闹钟小时 (0-23)
  int minute;         // 闹钟分钟 (0-59)
  bool enabled;       // 是否启用
  bool triggered;     // 是否已触发（防止重复触发）
};

Alarm alarms[3] = {     // 3个闹钟
  {7, 0, false, false},   // 闹钟1: 07:00, 禁用
  {12, 0, false, false},  // 闹钟2: 12:00, 禁用
  {18, 0, false, false}   // 闹钟3: 18:00, 禁用
};

int displayMode = 0;    // 显示模式 (0=时间, 1=闹钟1, 2=闹钟2, 3=闹钟3)
int currentAlarm = 0;   // 当前设置的闹钟 (0-2)
bool alarmRinging = false; // 是否正在响铃
unsigned long alarmStartTime = 0; // 闹钟开始时间
bool snoozeMode = false; // 贪睡模式
unsigned long snoozeTime = 0; // 贪睡开始时间

// === 函数声明 ===
void updateDisplay(String line1, String line2, String line3);
void updateStatusDisplay(unsigned long currentTime, int counter, String ledColor);
void updateClockDisplay();
void updateTimeFromMillis();
void handleButtonPress(bool keyA, bool keyB, bool keyC);
void initWiFi();
void syncTimeFromNTP();
void updateTimeFromNTP();
void checkAlarms();
void triggerAlarm(int alarmIndex);
void stopAlarm();
void updateAlarmDisplay();
void handleAlarmButtons(bool keyA, bool keyB, bool keyC);
void saveAlarmSettings();
void loadAlarmSettings();
void resetAlarmSettings();

// === 音符频率定义 ===
#define NOTE_C4  262   // 中央C
#define NOTE_D4  294   // D
#define NOTE_E4  330   // E
#define NOTE_F4  349   // F
#define NOTE_G4  392   // G
#define NOTE_A4  440   // A
#define NOTE_B4  494   // B
#define NOTE_C5  523   // 高音C

void setup() {
  Serial.begin(115200);
  delay(100); // 等待串口

  // === 硬件初始化 ===
  Serial.println("=== ESP32 时钟启动 ===");

  // 初始化数据存储
  preferences.begin("clock", false); // "clock"是命名空间
  loadAlarmSettings();
  Serial.println("✓ 闹钟设置已加载");

  // 初始化 WS2812B LED
  FastLED.addLeds<WS2812B, LED_PIN, GRB>(leds, NUM_LEDS);
  FastLED.setBrightness(50); // 设置亮度 (0-255)
  Serial.println("✓ WS2812B LED 初始化完成");

  // 初始化触摸按键 - 尝试不同的模式
  pinMode(KEY_A, INPUT_PULLUP);  // 上拉电阻
  pinMode(KEY_B, INPUT_PULLUP);
  pinMode(KEY_C, INPUT_PULLUP);
  Serial.println("✓ 触摸按键初始化完成（上拉模式）");
  Serial.println("触摸按键测试模式：请尝试触摸按键...");

  // 初始化蜂鸣器引脚
  pinMode(BUZZER_PIN, OUTPUT);
  Serial.println("✓ 蜂鸣器初始化完成");

  // 播放启动音效 (Do-Mi-So)
  Serial.println("播放启动音效...");
  tone(BUZZER_PIN, NOTE_C4, 200); delay(250);
  tone(BUZZER_PIN, NOTE_E4, 200); delay(250);
  tone(BUZZER_PIN, NOTE_G4, 300); delay(350);
  noTone(BUZZER_PIN);

  // 蜂鸣器测试 - 播放启动音
  Serial.println("播放启动音...");
  tone(BUZZER_PIN, NOTE_C4, 200); delay(250);
  tone(BUZZER_PIN, NOTE_E4, 200); delay(250);
  tone(BUZZER_PIN, NOTE_G4, 200); delay(250);
  noTone(BUZZER_PIN); // 停止声音

  // 打印硬件信息
  Serial.println("硬件配置:");
  Serial.println("- 主控: ESP32-WROOM-32");
  Serial.println("- 显示: 2.9寸电子墨水屏");
  Serial.println("- LED: WS2812B RGB (GPIO16)");
  Serial.println("- 按键: 3个触摸按键 (GPIO33,13,5)");
  Serial.println("- 蜂鸣器: 无源蜂鸣器 (GPIO25)");
  Serial.println("====================");

  // 初始化电子墨水屏
  display.init();
  display.setRotation(1); // 横屏显示
  Serial.println("✓ 电子墨水屏初始化完成");

  // 显示欢迎界面
  updateDisplay("启动中...", "硬件初始化", "请稍候");

  // LED 启动动画
  Serial.println("LED 启动动画...");
  leds[0] = CRGB::Red;   FastLED.show(); delay(300);
  leds[0] = CRGB::Green; FastLED.show(); delay(300);
  leds[0] = CRGB::Blue;  FastLED.show(); delay(300);
  leds[0] = CRGB::Black; FastLED.show(); // 关闭

  Serial.println("✓ 初始化完成！");

  // 初始化WiFi连接
  Serial.println("开始WiFi连接...");
  updateDisplay("WiFi连接中", "请稍候...", "");
  initWiFi();

  // 显示时钟界面
  updateClockDisplay();
  Serial.println("开始主循环...");
}

void loop() {
  static unsigned long lastSecond = 0;
  static unsigned long lastDisplay = 0;
  static unsigned long lastButton = 0;

  unsigned long currentTime = millis();

  // 每秒更新时间
  if (currentTime - lastSecond >= 1000) {
    if (timeSync) {
      updateTimeFromNTP();  // 使用网络时间
    } else {
      updateTimeFromMillis(); // 使用本地计时
    }
    lastSecond = currentTime;

    // 时间指示LED（每秒闪烁）
    if (!settingMode) {
      leds[0] = (currentSecond % 2 == 0) ? CRGB::Blue : CRGB::Black;
      FastLED.show();
    }
  }

  // 每5秒更新屏幕显示（非设置模式）
  if (currentTime - lastDisplay >= 5000 && !settingMode) {
    updateClockDisplay();
    lastDisplay = currentTime;
  }

  // 每小时重新同步时间（如果WiFi连接正常）
  static unsigned long lastSync = 0;
  if (currentTime - lastSync >= 3600000 && wifiConnected) { // 3600000ms = 1小时
    Serial.println("定时重新同步网络时间...");
    syncTimeFromNTP();
    lastSync = currentTime;
  }

  // 每100ms检查按键（防抖）
  if (currentTime - lastButton >= 100) {
    bool keyA = !digitalRead(KEY_A);
    bool keyB = !digitalRead(KEY_B);
    bool keyC = !digitalRead(KEY_C);

    if (alarmRinging) {
      // 闹钟响铃时，任意按键停止闹钟
      if (keyA || keyB || keyC) {
        stopAlarm();
      }
    } else {
      handleAlarmButtons(keyA, keyB, keyC);
    }
    lastButton = currentTime;
  }

  // 检查闹钟触发
  checkAlarms();

  // 每10秒打印详细调试信息
  static unsigned long lastDebug = 0;
  if (currentTime - lastDebug >= 10000) {
    Serial.println("=== 系统状态 ===");
    Serial.print("时间: ");
    if (currentHour < 10) Serial.print("0");
    Serial.print(currentHour);
    Serial.print(":");
    if (currentMinute < 10) Serial.print("0");
    Serial.print(currentMinute);
    Serial.print(":");
    if (currentSecond < 10) Serial.print("0");
    Serial.print(currentSecond);
    Serial.print(" 日期: ");
    Serial.print(currentYear);
    Serial.print("/");
    Serial.print(currentMonth);
    Serial.print("/");
    Serial.println(currentDay);

    Serial.print("WiFi状态: ");
    Serial.print(wifiConnected ? "已连接" : "未连接");
    if (wifiConnected) {
      Serial.print(" (");
      Serial.print(WiFi.localIP());
      Serial.print(")");
    }
    Serial.println();

    Serial.print("时间同步: ");
    Serial.println(timeSync ? "已同步" : "未同步");
    Serial.print("运行模式: ");
    Serial.println(settingMode ? "设置" : "正常");
    Serial.println("================");

    lastDebug = currentTime;
  }
}

// === 显示函数 ===
void updateDisplay(String line1, String line2, String line3) {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.setFont(&FreeMonoBold12pt7b);

    // 第一行
    display.setCursor(10, 40);
    display.print(line1);

    // 第二行
    display.setCursor(10, 80);
    display.print(line2);

    // 第三行
    display.setCursor(10, 120);
    display.print(line3);

  } while (display.nextPage());
}

void updateStatusDisplay(unsigned long currentTime, int counter, String ledColor) {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);

    // 标题
    display.setFont(&FreeMonoBold12pt7b);
    display.setCursor(10, 30);
    display.print("ESP32 时钟");

    // 运行时间
    display.setCursor(10, 60);
    display.print("运行: ");
    display.print(currentTime / 1000);
    display.print("s");

    // 循环次数
    display.setCursor(10, 90);
    display.print("循环: ");
    display.print(counter);

    // LED状态
    display.setCursor(10, 120);
    display.print("LED: ");
    display.print(ledColor);

    // 绘制一些装饰图形
    display.drawRect(5, 5, display.width()-10, display.height()-10, GxEPD_BLACK);
    display.drawLine(10, 140, display.width()-10, 140, GxEPD_BLACK);

  } while (display.nextPage());
}

// === 时钟功能函数 ===
void updateTimeFromMillis() {
  // 简单的时间计算（从启动开始）
  unsigned long totalSeconds = millis() / 1000;
  currentSecond = totalSeconds % 60;
  currentMinute = (totalSeconds / 60) % 60;
  currentHour = ((totalSeconds / 3600) + 12) % 24; // 从12点开始
}

void updateClockDisplay() {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);

    // 绘制边框
    display.drawRect(2, 2, display.width()-4, display.height()-4, GxEPD_BLACK);

    // 显示时间 (大字体，居中对齐)
    display.setFont(&FreeMonoBold12pt7b);
    display.setCursor(30, 35);  // 调整X坐标让时间居中
    if (currentHour < 10) display.print("0");
    display.print(currentHour);
    display.print(":");
    if (currentMinute < 10) display.print("0");
    display.print(currentMinute);
    display.print(":");
    if (currentSecond < 10) display.print("0");
    display.print(currentSecond);

    // 显示日期 (与时间对齐)
    display.setCursor(30, 65);  // 与时间相同的X坐标
    display.print(currentYear);
    display.print("/");
    if (currentMonth < 10) display.print("0");
    display.print(currentMonth);
    display.print("/");
    if (currentDay < 10) display.print("0");
    display.print(currentDay);

    // 分割线
    display.drawLine(8, 75, display.width()-8, 75, GxEPD_BLACK);

    // 显示WiFi和同步状态
    display.setCursor(8, 100);
    if (wifiConnected) {
      display.print("WiFi:");
      display.print(timeSync ? "已同步" : "未同步");
    } else {
      display.print("WiFi:未连接");
    }

    // 显示模式提示
    display.setCursor(8, 120);
    if (settingMode) {
      if (settingItem == 0) display.print("[调整小时]");
      else if (settingItem == 1) display.print("[调整分钟]");
      else display.print("[切换项目]");
    } else {
      display.print("A:设置 B:+ C:-");
    }

  } while (display.nextPage());
}

void handleButtonPress(bool keyA, bool keyB, bool keyC) {
  static bool lastKeyA = false, lastKeyB = false, lastKeyC = false;

  // 检测按键按下（边沿触发）
  bool keyA_pressed = keyA && !lastKeyA;
  bool keyB_pressed = keyB && !lastKeyB;
  bool keyC_pressed = keyC && !lastKeyC;

  if (keyA_pressed) {
    // A键：进入/退出设置模式
    settingMode = !settingMode;
    if (settingMode) {
      settingItem = 0; // 从设置小时开始
      tone(BUZZER_PIN, NOTE_C4, 100);
      leds[0] = CRGB::Yellow;
    } else {
      tone(BUZZER_PIN, NOTE_C5, 100);
      leds[0] = CRGB::Green;
    }
    FastLED.show();
    updateClockDisplay();
  }

  if (keyB_pressed && settingMode) {
    // B键：增加数值
    if (settingItem == 0) {
      currentHour = (currentHour + 1) % 24;
    } else if (settingItem == 1) {
      currentMinute = (currentMinute + 1) % 60;
    } else {
      settingItem = (settingItem + 1) % 3; // 切换设置项
    }
    tone(BUZZER_PIN, NOTE_E4, 100);
    leds[0] = CRGB::Blue;
    FastLED.show();
    updateClockDisplay();
  }

  if (keyC_pressed && settingMode) {
    // C键：减少数值
    if (settingItem == 0) {
      currentHour = (currentHour - 1 + 24) % 24;
    } else if (settingItem == 1) {
      currentMinute = (currentMinute - 1 + 60) % 60;
    } else {
      settingItem = (settingItem - 1 + 3) % 3; // 切换设置项
    }
    tone(BUZZER_PIN, NOTE_G4, 100);
    leds[0] = CRGB::Red;
    FastLED.show();
    updateClockDisplay();
  }

  // 保存按键状态
  lastKeyA = keyA;
  lastKeyB = keyB;
  lastKeyC = keyC;
}

// === WiFi 和时间同步功能 ===
void initWiFi() {
  WiFi.begin(ssid, password);
  Serial.print("连接WiFi");

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
  }

  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    Serial.println();
    Serial.print("✓ WiFi连接成功！IP地址: ");
    Serial.println(WiFi.localIP());

    // 连接成功后同步时间
    syncTimeFromNTP();

    // LED指示连接成功
    leds[0] = CRGB::Green;
    FastLED.show();
    tone(BUZZER_PIN, NOTE_C5, 200);
    delay(300);

  } else {
    wifiConnected = false;
    Serial.println();
    Serial.println("✗ WiFi连接失败，将使用手动时间");

    // LED指示连接失败
    leds[0] = CRGB::Red;
    FastLED.show();
    tone(BUZZER_PIN, NOTE_C4, 500);
    delay(600);
  }

  leds[0] = CRGB::Black;
  FastLED.show();
}

void syncTimeFromNTP() {
  if (!wifiConnected) return;

  Serial.println("同步网络时间...");
  configTime(gmtOffset_sec, daylightOffset_sec, ntpServer);

  // 等待时间同步
  int attempts = 0;
  while (!time(nullptr) && attempts < 10) {
    delay(1000);
    Serial.print(".");
    attempts++;
  }

  if (time(nullptr)) {
    timeSync = true;
    updateTimeFromNTP();
    Serial.println();
    Serial.println("✓ 时间同步成功！");

    // 音效提示同步成功
    tone(BUZZER_PIN, NOTE_E4, 100); delay(150);
    tone(BUZZER_PIN, NOTE_G4, 100); delay(150);
    tone(BUZZER_PIN, NOTE_C5, 200);
  } else {
    timeSync = false;
    Serial.println();
    Serial.println("✗ 时间同步失败");
  }
}

void updateTimeFromNTP() {
  if (!timeSync) return;

  time_t now;
  struct tm timeinfo;
  time(&now);
  localtime_r(&now, &timeinfo);

  currentHour = timeinfo.tm_hour;
  currentMinute = timeinfo.tm_min;
  currentSecond = timeinfo.tm_sec;
  currentDay = timeinfo.tm_mday;
  currentMonth = timeinfo.tm_mon + 1;  // tm_mon 是 0-11
  currentYear = timeinfo.tm_year + 1900; // tm_year 从1900年开始
}

// === 闹钟功能函数 ===
void checkAlarms() {
  if (alarmRinging) return; // 如果已经在响铃，不检查其他闹钟

  for (int i = 0; i < 3; i++) {
    if (alarms[i].enabled &&
        alarms[i].hour == currentHour &&
        alarms[i].minute == currentMinute &&
        currentSecond == 0 &&
        !alarms[i].triggered) {

      triggerAlarm(i);
      alarms[i].triggered = true;
      break;
    }

    // 重置触发状态（当分钟改变时）
    if (alarms[i].minute != currentMinute) {
      alarms[i].triggered = false;
    }
  }

  // 检查贪睡
  if (snoozeMode && millis() - snoozeTime >= 300000) { // 5分钟
    snoozeMode = false;
    triggerAlarm(currentAlarm);
  }
}

void triggerAlarm(int alarmIndex) {
  alarmRinging = true;
  alarmStartTime = millis();
  currentAlarm = alarmIndex;

  Serial.print("闹钟 ");
  Serial.print(alarmIndex + 1);
  Serial.println(" 响铃！");

  // 更新显示
  updateAlarmDisplay();
}

void stopAlarm() {
  alarmRinging = false;
  snoozeMode = false;

  // 停止声音和LED
  noTone(BUZZER_PIN);
  leds[0] = CRGB::Black;
  FastLED.show();

  Serial.println("闹钟已停止");

  // 恢复正常显示
  displayMode = 0;
  updateClockDisplay();
}

void updateAlarmDisplay() {
  display.setFullWindow();
  display.firstPage();
  do {
    display.fillScreen(GxEPD_WHITE);
    display.setTextColor(GxEPD_BLACK);
    display.drawRect(2, 2, display.width()-4, display.height()-4, GxEPD_BLACK);

    if (alarmRinging) {
      // 闹钟响铃显示
      display.setFont(&FreeMonoBold12pt7b);
      display.setCursor(20, 40);
      display.print("闹钟响铃!");

      display.setCursor(30, 70);
      display.print("闹钟 ");
      display.print(currentAlarm + 1);

      display.setCursor(15, 100);
      display.print("按任意键停止");

    } else if (displayMode > 0) {
      // 闹钟设置显示
      int alarmIdx = displayMode - 1;
      display.setFont(&FreeMonoBold12pt7b);

      display.setCursor(20, 35);
      display.print("闹钟 ");
      display.print(alarmIdx + 1);

      display.setCursor(30, 65);
      if (alarms[alarmIdx].hour < 10) display.print("0");
      display.print(alarms[alarmIdx].hour);
      display.print(":");
      if (alarms[alarmIdx].minute < 10) display.print("0");
      display.print(alarms[alarmIdx].minute);

      display.setCursor(8, 95);
      display.print("状态: ");
      display.print(alarms[alarmIdx].enabled ? "开启" : "关闭");

      display.setCursor(8, 120);
      if (settingMode) {
        display.print("A:项目 B:+ C:-");
      } else {
        display.print("A:切换 B:开关 C:设置");
      }
    }

  } while (display.nextPage());

  // 闹钟响铃时的LED和声音效果
  if (alarmRinging) {
    static unsigned long lastEffect = 0;
    static bool effectState = false;

    if (millis() - lastEffect >= 500) { // 每500ms切换
      effectState = !effectState;

      if (effectState) {
        leds[0] = CRGB::Red;
        tone(BUZZER_PIN, NOTE_C5, 200);
      } else {
        leds[0] = CRGB::Yellow;
        tone(BUZZER_PIN, NOTE_E4, 200);
      }
      FastLED.show();
      lastEffect = millis();
    }
  }
}

void handleAlarmButtons(bool keyA, bool keyB, bool keyC) {
  static bool lastKeyA = false, lastKeyB = false, lastKeyC = false;

  // 检测按键按下（边沿触发）
  bool keyA_pressed = keyA && !lastKeyA;
  bool keyB_pressed = keyB && !lastKeyB;
  bool keyC_pressed = keyC && !lastKeyC;

  if (keyA_pressed) {
    if (settingMode) {
      // 设置模式：切换设置项
      settingItem = (settingItem + 1) % 3; // 0=小时, 1=分钟, 2=退出
      if (settingItem == 2) {
        settingMode = false;
        saveAlarmSettings(); // 保存设置
        displayMode = 0; // 回到时间显示
        updateClockDisplay();
        Serial.println("闹钟设置已保存");
      } else {
        updateAlarmDisplay();
      }
    } else {
      // 正常模式：切换显示模式
      displayMode = (displayMode + 1) % 4; // 0=时间, 1-3=闹钟1-3
      if (displayMode == 0) {
        updateClockDisplay();
      } else {
        updateAlarmDisplay();
      }
    }
    tone(BUZZER_PIN, NOTE_C4, 100);
  }

  if (keyB_pressed) {
    if (settingMode && displayMode > 0) {
      // 设置模式：增加数值
      int alarmIdx = displayMode - 1;
      if (settingItem == 0) {
        alarms[alarmIdx].hour = (alarms[alarmIdx].hour + 1) % 24;
      } else if (settingItem == 1) {
        alarms[alarmIdx].minute = (alarms[alarmIdx].minute + 1) % 60;
      }
      updateAlarmDisplay();
    } else if (displayMode > 0) {
      // 正常模式：开关闹钟
      int alarmIdx = displayMode - 1;
      alarms[alarmIdx].enabled = !alarms[alarmIdx].enabled;
      updateAlarmDisplay();
      saveAlarmSettings(); // 自动保存
      Serial.print("闹钟 ");
      Serial.print(alarmIdx + 1);
      Serial.println(alarms[alarmIdx].enabled ? " 已开启" : " 已关闭");
    }
    tone(BUZZER_PIN, NOTE_E4, 100);
  }

  if (keyC_pressed) {
    if (displayMode > 0 && !settingMode) {
      // 进入设置模式
      settingMode = true;
      settingItem = 0;
      updateAlarmDisplay();
    } else if (settingMode && displayMode > 0) {
      // 设置模式：减少数值
      int alarmIdx = displayMode - 1;
      if (settingItem == 0) {
        alarms[alarmIdx].hour = (alarms[alarmIdx].hour - 1 + 24) % 24;
      } else if (settingItem == 1) {
        alarms[alarmIdx].minute = (alarms[alarmIdx].minute - 1 + 60) % 60;
      }
      updateAlarmDisplay();
    }
    tone(BUZZER_PIN, NOTE_G4, 100);
  }

  // 保存按键状态
  lastKeyA = keyA;
  lastKeyB = keyB;
  lastKeyC = keyC;
}

// === 数据存储功能 ===
void saveAlarmSettings() {
  Serial.println("保存闹钟设置到存储器...");

  for (int i = 0; i < 3; i++) {
    String hourKey = "alarm" + String(i) + "_hour";
    String minuteKey = "alarm" + String(i) + "_minute";
    String enabledKey = "alarm" + String(i) + "_enabled";

    preferences.putInt(hourKey.c_str(), alarms[i].hour);
    preferences.putInt(minuteKey.c_str(), alarms[i].minute);
    preferences.putBool(enabledKey.c_str(), alarms[i].enabled);
  }

  Serial.println("✓ 闹钟设置保存完成");
}

void loadAlarmSettings() {
  Serial.println("从存储器加载闹钟设置...");

  for (int i = 0; i < 3; i++) {
    String hourKey = "alarm" + String(i) + "_hour";
    String minuteKey = "alarm" + String(i) + "_minute";
    String enabledKey = "alarm" + String(i) + "_enabled";

    // 加载设置，如果不存在则使用默认值
    alarms[i].hour = preferences.getInt(hourKey.c_str(), alarms[i].hour);
    alarms[i].minute = preferences.getInt(minuteKey.c_str(), alarms[i].minute);
    alarms[i].enabled = preferences.getBool(enabledKey.c_str(), alarms[i].enabled);

    Serial.print("闹钟 ");
    Serial.print(i + 1);
    Serial.print(": ");
    if (alarms[i].hour < 10) Serial.print("0");
    Serial.print(alarms[i].hour);
    Serial.print(":");
    if (alarms[i].minute < 10) Serial.print("0");
    Serial.print(alarms[i].minute);
    Serial.print(" (");
    Serial.print(alarms[i].enabled ? "开启" : "关闭");
    Serial.println(")");
  }
}

void resetAlarmSettings() {
  Serial.println("重置闹钟设置为默认值...");

  // 恢复默认设置
  alarms[0] = {7, 0, false, false};   // 闹钟1: 07:00, 禁用
  alarms[1] = {12, 0, false, false};  // 闹钟2: 12:00, 禁用
  alarms[2] = {18, 0, false, false};  // 闹钟3: 18:00, 禁用

  // 清除存储的设置
  preferences.clear();

  // 保存默认设置
  saveAlarmSettings();

  Serial.println("✓ 闹钟设置已重置");
}
