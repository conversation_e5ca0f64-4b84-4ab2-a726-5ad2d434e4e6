#ifndef ALARM_H
#define ALARM_H

#include <Arduino.h>
#include "config.h"

// === 闹钟数据结构 ===
struct Alarm {
  int hour;           // 闹钟小时 (0-23)
  int minute;         // 闹钟分钟 (0-59)
  bool enabled;       // 是否启用
  bool triggered;     // 是否已触发（防止重复触发）
};

// === 闹钟状态变量 ===
extern Alarm alarms[ALARM_COUNT];
extern bool alarmRinging;
extern unsigned long alarmStartTime;
extern int currentAlarm;
extern bool snoozeMode;
extern unsigned long snoozeTime;

// === 闹钟管理函数 ===
void initAlarms();
void checkAlarms();
void triggerAlarm(int alarmIndex);
void stopAlarm();
void snoozeAlarm();
int getEnabledAlarmCount();

#endif // ALARM_H
