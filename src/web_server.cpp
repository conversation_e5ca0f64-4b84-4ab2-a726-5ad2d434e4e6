#include "web_server.h"
#include "wifi_manager.h"
#include "alarm.h"
#include "display.h"
#include "storage.h"

// === Web服务器对象定义 ===
WebServer server(80);

void initWebServer() {
  if (!wifiConnected) {
    Serial.println("WiFi未连接，无法启动Web服务器");
    return;
  }
  
  // 设置路由
  server.on("/", handleRoot);
  server.on("/status", handleStatus);
  server.on("/alarms", handleAlarms);
  server.on("/set-alarm", HTTP_POST, handleSetAlarm);
  server.on("/api/status", apiGetStatus);
  server.on("/api/alarms", HTTP_GET, apiGetAlarms);
  server.on("/api/alarms", HTTP_POST, apiSetAlarm);
  server.on("/api/toggle-alarm", HTTP_POST, apiToggleAlarm);
  server.onNotFound(handleNotFound);
  
  server.begin();
  Serial.println("✓ Web服务器启动成功");
  Serial.print("访问地址: http://");
  Serial.println(WiFi.localIP());
}

void handleWebServer() {
  server.handleClient();
}

void stopWebServer() {
  server.stop();
  Serial.println("Web服务器已停止");
}

// === 网页处理函数 ===
void handleRoot() {
  server.send(200, "text/html", generateMainPage());
}

void handleStatus() {
  server.send(200, "text/html", generateStatusPage());
}

void handleAlarms() {
  server.send(200, "text/html", generateAlarmPage());
}

void handleSetAlarm() {
  if (server.hasArg("alarm") && server.hasArg("hour") && server.hasArg("minute")) {
    int alarmIndex = server.arg("alarm").toInt();
    int hour = server.arg("hour").toInt();
    int minute = server.arg("minute").toInt();
    bool enabled = server.hasArg("enabled");
    
    if (alarmIndex >= 0 && alarmIndex < ALARM_COUNT) {
      alarms[alarmIndex].hour = hour;
      alarms[alarmIndex].minute = minute;
      alarms[alarmIndex].enabled = enabled;
      saveAlarmSettings();
      
      server.send(200, "text/plain", "闹钟设置成功");
      Serial.print("Web设置闹钟 ");
      Serial.print(alarmIndex + 1);
      Serial.print(": ");
      Serial.print(hour);
      Serial.print(":");
      Serial.println(minute);
    } else {
      server.send(400, "text/plain", "无效的闹钟索引");
    }
  } else {
    server.send(400, "text/plain", "缺少必要参数");
  }
}

void handleNotFound() {
  server.send(404, "text/plain", "页面未找到");
}

// === API接口函数 ===
void apiGetStatus() {
  DynamicJsonDocument doc(1024);
  
  doc["time"]["hour"] = currentHour;
  doc["time"]["minute"] = currentMinute;
  doc["time"]["second"] = currentSecond;
  doc["date"]["year"] = currentYear;
  doc["date"]["month"] = currentMonth;
  doc["date"]["day"] = currentDay;
  doc["wifi"]["connected"] = wifiConnected;
  doc["wifi"]["ip"] = WiFi.localIP().toString();
  doc["wifi"]["timeSync"] = timeSync;
  doc["system"]["uptime"] = millis() / 1000;
  doc["system"]["freeHeap"] = ESP.getFreeHeap();
  
  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

void apiGetAlarms() {
  DynamicJsonDocument doc(1024);
  
  for (int i = 0; i < ALARM_COUNT; i++) {
    doc["alarms"][i]["hour"] = alarms[i].hour;
    doc["alarms"][i]["minute"] = alarms[i].minute;
    doc["alarms"][i]["enabled"] = alarms[i].enabled;
  }
  
  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

void apiSetAlarm() {
  DynamicJsonDocument doc(512);
  deserializeJson(doc, server.arg("plain"));
  
  int alarmIndex = doc["alarm"];
  if (alarmIndex >= 0 && alarmIndex < ALARM_COUNT) {
    alarms[alarmIndex].hour = doc["hour"];
    alarms[alarmIndex].minute = doc["minute"];
    alarms[alarmIndex].enabled = doc["enabled"];
    saveAlarmSettings();
    
    server.send(200, "application/json", "{\"success\":true}");
  } else {
    server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid alarm index\"}");
  }
}

void apiToggleAlarm() {
  int alarmIndex = server.arg("alarm").toInt();
  if (alarmIndex >= 0 && alarmIndex < ALARM_COUNT) {
    alarms[alarmIndex].enabled = !alarms[alarmIndex].enabled;
    saveAlarmSettings();
    
    DynamicJsonDocument doc(256);
    doc["success"] = true;
    doc["enabled"] = alarms[alarmIndex].enabled;
    
    String response;
    serializeJson(doc, response);
    server.send(200, "application/json", response);
  } else {
    server.send(400, "application/json", "{\"success\":false}");
  }
}

// === 网页内容生成 ===
String generateMainPage() {
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 智能时钟</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .time-display { font-size: 3em; text-align: center; color: #2196F3; margin: 20px 0; }
        .date-display { font-size: 1.5em; text-align: center; color: #666; margin-bottom: 30px; }
        .nav { display: flex; justify-content: center; gap: 10px; margin-bottom: 30px; }
        .nav a { padding: 10px 20px; background: #2196F3; color: white; text-decoration: none; border-radius: 5px; }
        .nav a:hover { background: #1976D2; }
        .status { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .status-item { padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #2196F3; }
        .status-label { font-weight: bold; color: #333; }
        .status-value { color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">🕐 ESP32 智能时钟</h1>

        <div class="time-display" id="time">--:--:--</div>
        <div class="date-display" id="date">----/--/--</div>

        <div class="nav">
            <a href="/">首页</a>
            <a href="/alarms">闹钟设置</a>
            <a href="/status">系统状态</a>
        </div>

        <div class="status">
            <div class="status-item">
                <div class="status-label">WiFi状态</div>
                <div class="status-value" id="wifi-status">连接中...</div>
            </div>
            <div class="status-item">
                <div class="status-label">时间同步</div>
                <div class="status-value" id="time-sync">检查中...</div>
            </div>
            <div class="status-item">
                <div class="status-label">运行时间</div>
                <div class="status-value" id="uptime">计算中...</div>
            </div>
            <div class="status-item">
                <div class="status-label">内存使用</div>
                <div class="status-value" id="memory">检查中...</div>
            </div>
        </div>
    </div>

    <script>
        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('time').textContent =
                        String(data.time.hour).padStart(2, '0') + ':' +
                        String(data.time.minute).padStart(2, '0') + ':' +
                        String(data.time.second).padStart(2, '0');

                    document.getElementById('date').textContent =
                        data.date.year + '/' +
                        String(data.date.month).padStart(2, '0') + '/' +
                        String(data.date.day).padStart(2, '0');

                    document.getElementById('wifi-status').textContent =
                        data.wifi.connected ? '已连接 (' + data.wifi.ip + ')' : '未连接';

                    document.getElementById('time-sync').textContent =
                        data.wifi.timeSync ? '已同步' : '未同步';

                    document.getElementById('uptime').textContent =
                        Math.floor(data.system.uptime / 3600) + '小时 ' +
                        Math.floor((data.system.uptime % 3600) / 60) + '分钟';

                    document.getElementById('memory').textContent =
                        Math.round(data.system.freeHeap / 1024) + ' KB 可用';
                });
        }

        updateStatus();
        setInterval(updateStatus, 2000); // 每2秒更新一次
    </script>
</body>
</html>
)";
  return html;
}

String generateAlarmPage() {
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闹钟设置 - ESP32时钟</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .nav { display: flex; justify-content: center; gap: 10px; margin-bottom: 30px; }
        .nav a { padding: 10px 20px; background: #2196F3; color: white; text-decoration: none; border-radius: 5px; }
        .nav a:hover { background: #1976D2; }
        .alarm-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; background: #f8f9fa; }
        .alarm-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .alarm-title { font-size: 1.2em; font-weight: bold; color: #333; }
        .alarm-toggle { position: relative; display: inline-block; width: 60px; height: 34px; }
        .alarm-toggle input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .4s; border-radius: 34px; }
        .slider:before { position: absolute; content: ""; height: 26px; width: 26px; left: 4px; bottom: 4px; background-color: white; transition: .4s; border-radius: 50%; }
        input:checked + .slider { background-color: #2196F3; }
        input:checked + .slider:before { transform: translateX(26px); }
        .time-input { display: flex; gap: 10px; align-items: center; }
        .time-input input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 1.1em; width: 60px; text-align: center; }
        .save-btn { background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        .save-btn:hover { background: #45a049; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">⏰ 闹钟设置</h1>

        <div class="nav">
            <a href="/">首页</a>
            <a href="/alarms">闹钟设置</a>
            <a href="/status">系统状态</a>
        </div>

        <div id="alarms-container">
            <!-- 闹钟列表将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        function loadAlarms() {
            fetch('/api/alarms')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('alarms-container');
                    container.innerHTML = '';

                    data.alarms.forEach((alarm, index) => {
                        const alarmCard = document.createElement('div');
                        alarmCard.className = 'alarm-card';
                        alarmCard.innerHTML = `
                            <div class="alarm-header">
                                <div class="alarm-title">闹钟 ${index + 1}</div>
                                <label class="alarm-toggle">
                                    <input type="checkbox" ${alarm.enabled ? 'checked' : ''}
                                           onchange="toggleAlarm(${index})">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="time-input">
                                <input type="number" min="0" max="23" value="${String(alarm.hour).padStart(2, '0')}"
                                       id="hour-${index}" placeholder="时">
                                <span>:</span>
                                <input type="number" min="0" max="59" value="${String(alarm.minute).padStart(2, '0')}"
                                       id="minute-${index}" placeholder="分">
                                <button class="save-btn" onclick="saveAlarm(${index})">保存</button>
                            </div>
                        `;
                        container.appendChild(alarmCard);
                    });
                });
        }

        function toggleAlarm(index) {
            fetch('/api/toggle-alarm', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `alarm=${index}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log(`闹钟 ${index + 1} ${data.enabled ? '已开启' : '已关闭'}`);
                }
            });
        }

        function saveAlarm(index) {
            const hour = document.getElementById(`hour-${index}`).value;
            const minute = document.getElementById(`minute-${index}`).value;
            const enabled = document.querySelector(`input[onchange="toggleAlarm(${index})"]`).checked;

            const data = {
                alarm: index,
                hour: parseInt(hour),
                minute: parseInt(minute),
                enabled: enabled
            };

            fetch('/api/alarms', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert(`闹钟 ${index + 1} 设置成功！`);
                } else {
                    alert('设置失败，请重试');
                }
            });
        }

        loadAlarms();
    </script>
</body>
</html>
)";
  return html;
}

String generateStatusPage() {
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - ESP32时钟</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .nav { display: flex; justify-content: center; gap: 10px; margin-bottom: 30px; }
        .nav a { padding: 10px 20px; background: #2196F3; color: white; text-decoration: none; border-radius: 5px; }
        .nav a:hover { background: #1976D2; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .status-section { border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: #f8f9fa; }
        .section-title { font-size: 1.3em; font-weight: bold; color: #333; margin-bottom: 15px; border-bottom: 2px solid #2196F3; padding-bottom: 5px; }
        .status-item { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .status-label { font-weight: bold; color: #555; }
        .status-value { color: #333; }
        .refresh-btn { background: #2196F3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 20px; }
        .refresh-btn:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">📊 系统状态</h1>

        <div class="nav">
            <a href="/">首页</a>
            <a href="/alarms">闹钟设置</a>
            <a href="/status">系统状态</a>
        </div>

        <div class="status-grid">
            <div class="status-section">
                <div class="section-title">⏰ 时间信息</div>
                <div class="status-item">
                    <span class="status-label">当前时间:</span>
                    <span class="status-value" id="current-time">--:--:--</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前日期:</span>
                    <span class="status-value" id="current-date">----/--/--</span>
                </div>
                <div class="status-item">
                    <span class="status-label">时间同步:</span>
                    <span class="status-value" id="time-sync-status">检查中...</span>
                </div>
            </div>

            <div class="status-section">
                <div class="section-title">📶 网络信息</div>
                <div class="status-item">
                    <span class="status-label">WiFi状态:</span>
                    <span class="status-value" id="wifi-connected">检查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">IP地址:</span>
                    <span class="status-value" id="ip-address">获取中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">信号强度:</span>
                    <span class="status-value" id="wifi-signal">检查中...</span>
                </div>
            </div>

            <div class="status-section">
                <div class="section-title">💻 系统信息</div>
                <div class="status-item">
                    <span class="status-label">运行时间:</span>
                    <span class="status-value" id="system-uptime">计算中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">可用内存:</span>
                    <span class="status-value" id="free-memory">检查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">芯片型号:</span>
                    <span class="status-value">ESP32-WROOM-32</span>
                </div>
            </div>

            <div class="status-section">
                <div class="section-title">⏰ 闹钟状态</div>
                <div id="alarm-status">
                    <!-- 闹钟状态将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <button class="refresh-btn" onclick="updateAllStatus()">🔄 刷新状态</button>
    </div>

    <script>
        function updateAllStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    // 时间信息
                    document.getElementById('current-time').textContent =
                        String(data.time.hour).padStart(2, '0') + ':' +
                        String(data.time.minute).padStart(2, '0') + ':' +
                        String(data.time.second).padStart(2, '0');

                    document.getElementById('current-date').textContent =
                        data.date.year + '/' +
                        String(data.date.month).padStart(2, '0') + '/' +
                        String(data.date.day).padStart(2, '0');

                    document.getElementById('time-sync-status').textContent =
                        data.wifi.timeSync ? '✅ 已同步' : '❌ 未同步';

                    // 网络信息
                    document.getElementById('wifi-connected').textContent =
                        data.wifi.connected ? '✅ 已连接' : '❌ 未连接';

                    document.getElementById('ip-address').textContent =
                        data.wifi.connected ? data.wifi.ip : '未分配';

                    // 系统信息
                    const uptime = data.system.uptime;
                    const hours = Math.floor(uptime / 3600);
                    const minutes = Math.floor((uptime % 3600) / 60);
                    const seconds = uptime % 60;
                    document.getElementById('system-uptime').textContent =
                        `${hours}时${minutes}分${seconds}秒`;

                    document.getElementById('free-memory').textContent =
                        Math.round(data.system.freeHeap / 1024) + ' KB';
                });

            // 加载闹钟状态
            fetch('/api/alarms')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('alarm-status');
                    container.innerHTML = '';

                    data.alarms.forEach((alarm, index) => {
                        const alarmItem = document.createElement('div');
                        alarmItem.className = 'status-item';
                        alarmItem.innerHTML = `
                            <span class="status-label">闹钟 ${index + 1}:</span>
                            <span class="status-value">
                                ${String(alarm.hour).padStart(2, '0')}:${String(alarm.minute).padStart(2, '0')}
                                ${alarm.enabled ? '✅' : '❌'}
                            </span>
                        `;
                        container.appendChild(alarmItem);
                    });
                });
        }

        updateAllStatus();
        setInterval(updateAllStatus, 5000); // 每5秒自动刷新
    </script>
</body>
</html>
)";
  return html;
}
