#include "web_server.h"
#include "wifi_manager.h"
#include "alarm.h"
#include "display.h"
#include "storage.h"

// === Web服务器对象定义 ===
WebServer server(80);

void initWebServer() {
  if (!wifiConnected) {
    Serial.println("WiFi未连接，无法启动Web服务器");
    return;
  }
  
  // 设置路由
  server.on("/", handleRoot);
  server.on("/status", handleStatus);
  server.on("/alarms", handleAlarms);
  server.on("/set-alarm", HTTP_POST, handleSetAlarm);
  server.on("/api/status", apiGetStatus);
  server.on("/api/alarms", HTTP_GET, apiGetAlarms);
  server.on("/api/alarms", HTTP_POST, apiSetAlarm);
  server.on("/api/toggle-alarm", HTTP_POST, apiToggleAlarm);
  server.onNotFound(handleNotFound);
  
  server.begin();
  Serial.println("✓ Web服务器启动成功");
  Serial.print("访问地址: http://");
  Serial.println(WiFi.localIP());
}

void handleWebServer() {
  server.handleClient();
}

void stopWebServer() {
  server.stop();
  Serial.println("Web服务器已停止");
}

// === 网页处理函数 ===
void handleRoot() {
  server.send(200, "text/html", generateMainPage());
}

void handleStatus() {
  server.send(200, "text/html", generateStatusPage());
}

void handleAlarms() {
  server.send(200, "text/html", generateAlarmPage());
}

void handleSetAlarm() {
  if (server.hasArg("alarm") && server.hasArg("hour") && server.hasArg("minute")) {
    int alarmIndex = server.arg("alarm").toInt();
    int hour = server.arg("hour").toInt();
    int minute = server.arg("minute").toInt();
    bool enabled = server.hasArg("enabled");
    
    if (alarmIndex >= 0 && alarmIndex < ALARM_COUNT) {
      alarms[alarmIndex].hour = hour;
      alarms[alarmIndex].minute = minute;
      alarms[alarmIndex].enabled = enabled;
      saveAlarmSettings();
      
      server.send(200, "text/plain", "Alarm set successfully");
      Serial.print("Web alarm set ");
      Serial.print(alarmIndex + 1);
      Serial.print(": ");
      Serial.print(hour);
      Serial.print(":");
      Serial.println(minute);
    } else {
      server.send(400, "text/plain", "Invalid alarm index");
    }
  } else {
    server.send(400, "text/plain", "Missing required parameters");
  }
}

void handleNotFound() {
  server.send(404, "text/plain", "Page not found");
}

// === API接口函数 ===
void apiGetStatus() {
  JsonDocument doc;
  
  doc["time"]["hour"] = currentHour;
  doc["time"]["minute"] = currentMinute;
  doc["time"]["second"] = currentSecond;
  doc["date"]["year"] = currentYear;
  doc["date"]["month"] = currentMonth;
  doc["date"]["day"] = currentDay;
  doc["wifi"]["connected"] = wifiConnected;
  doc["wifi"]["ip"] = WiFi.localIP().toString();
  doc["wifi"]["timeSync"] = timeSync;
  doc["system"]["uptime"] = millis() / 1000;
  doc["system"]["freeHeap"] = ESP.getFreeHeap();
  
  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

void apiGetAlarms() {
  JsonDocument doc;
  
  for (int i = 0; i < ALARM_COUNT; i++) {
    doc["alarms"][i]["hour"] = alarms[i].hour;
    doc["alarms"][i]["minute"] = alarms[i].minute;
    doc["alarms"][i]["enabled"] = alarms[i].enabled;
  }
  
  String response;
  serializeJson(doc, response);
  server.send(200, "application/json", response);
}

void apiSetAlarm() {
  JsonDocument doc;
  deserializeJson(doc, server.arg("plain"));
  
  int alarmIndex = doc["alarm"];
  if (alarmIndex >= 0 && alarmIndex < ALARM_COUNT) {
    alarms[alarmIndex].hour = doc["hour"];
    alarms[alarmIndex].minute = doc["minute"];
    alarms[alarmIndex].enabled = doc["enabled"];
    saveAlarmSettings();
    
    server.send(200, "application/json", "{\"success\":true}");
  } else {
    server.send(400, "application/json", "{\"success\":false,\"error\":\"Invalid alarm index\"}");
  }
}

void apiToggleAlarm() {
  int alarmIndex = server.arg("alarm").toInt();
  if (alarmIndex >= 0 && alarmIndex < ALARM_COUNT) {
    alarms[alarmIndex].enabled = !alarms[alarmIndex].enabled;
    saveAlarmSettings();
    
    JsonDocument doc;
    doc["success"] = true;
    doc["enabled"] = alarms[alarmIndex].enabled;
    
    String response;
    serializeJson(doc, response);
    server.send(200, "application/json", response);
  } else {
    server.send(400, "application/json", "{\"success\":false}");
  }
}

// === 网页内容生成 ===
String generateMainPage() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<meta charset='UTF-8'>";
  html += "<title>ESP32 Smart Clock</title>";
  html += "<style>";
  html += "body{font-family:Arial;margin:20px;background:#f0f0f0;}";
  html += ".container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;}";
  html += ".time{font-size:2em;text-align:center;color:#2196F3;margin:20px 0;}";
  html += ".nav{text-align:center;margin:20px 0;}";
  html += ".nav a{padding:10px 15px;background:#2196F3;color:white;text-decoration:none;margin:5px;border-radius:5px;}";
  html += ".status{margin:20px 0;}";
  html += ".status div{margin:10px 0;padding:10px;background:#f8f9fa;border-radius:5px;}";
  html += "</style></head><body>";
  html += "<div class='container'>";
  html += "<h1 style='text-align:center;'>ESP32 Smart Clock</h1>";
  html += "<div class='time' id='time'>--:--:--</div>";
  html += "<div class='nav'>";
  html += "<a href='/'>Home</a>";
  html += "<a href='/alarms'>Alarms</a>";
  html += "<a href='/status'>Status</a>";
  html += "</div>";
  html += "<div class='status'>";
  html += "<div>WiFi: <span id='wifi'>Checking...</span></div>";
  html += "<div>Time Sync: <span id='sync'>Checking...</span></div>";
  html += "<div>Uptime: <span id='uptime'>Calculating...</span></div>";
  html += "<div>Memory: <span id='memory'>Checking...</span></div>";
  html += "</div></div>";
  html += "<script>";
  html += "function update(){";
  html += "fetch('/api/status').then(r=>r.json()).then(d=>{";
  html += "document.getElementById('time').textContent=";
  html += "String(d.time.hour).padStart(2,'0')+':'+String(d.time.minute).padStart(2,'0')+':'+String(d.time.second).padStart(2,'0');";
  html += "document.getElementById('wifi').textContent=d.wifi.connected?'Connected':'Disconnected';";
  html += "document.getElementById('sync').textContent=d.wifi.timeSync?'Synced':'Not synced';";
  html += "document.getElementById('uptime').textContent=Math.floor(d.system.uptime/3600)+'h '+Math.floor((d.system.uptime%3600)/60)+'m';";
  html += "document.getElementById('memory').textContent=Math.round(d.system.freeHeap/1024)+' KB';";
  html += "});}";
  html += "update();setInterval(update,2000);";
  html += "</script></body></html>";
  return html;
}

String generateAlarmPage() {
  return "<!DOCTYPE html><html><head><title>Alarm Settings</title><style>body{font-family:Arial;margin:20px;background:#f0f0f0;}.container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;}.alarm{border:1px solid #ddd;margin:10px 0;padding:15px;border-radius:5px;}.nav a{padding:10px 15px;background:#2196F3;color:white;text-decoration:none;margin:5px;border-radius:5px;}input{padding:5px;margin:5px;border:1px solid #ddd;border-radius:3px;}button{padding:8px 15px;background:#4CAF50;color:white;border:none;border-radius:3px;cursor:pointer;}</style></head><body><div class='container'><h1 style='text-align:center;'>Alarm Settings</h1><div style='text-align:center;margin:20px 0;'><a href='/'>Home</a><a href='/alarms'>Alarms</a><a href='/status'>Status</a></div><div id='alarms'></div></div><script>function loadAlarms(){fetch('/api/alarms').then(r=>r.json()).then(d=>{let html='';d.alarms.forEach((a,i)=>{html+='<div class=\"alarm\">';html+='<h3>Alarm '+(i+1)+'</h3>';html+='<input type=\"checkbox\" id=\"en'+i+'\" '+(a.enabled?'checked':'')+' onchange=\"toggle('+i+')\"> Enabled<br>';html+='<input type=\"number\" min=\"0\" max=\"23\" value=\"'+a.hour+'\" id=\"h'+i+'\" style=\"width:50px\"> : ';html+='<input type=\"number\" min=\"0\" max=\"59\" value=\"'+a.minute+'\" id=\"m'+i+'\" style=\"width:50px\">';html+='<button onclick=\"save('+i+')\">Save</button>';html+='</div>';});document.getElementById('alarms').innerHTML=html;});}function toggle(i){fetch('/api/toggle-alarm',{method:'POST',headers:{'Content-Type':'application/x-www-form-urlencoded'},body:'alarm='+i});}function save(i){let h=document.getElementById('h'+i).value;let m=document.getElementById('m'+i).value;let e=document.getElementById('en'+i).checked;fetch('/api/alarms',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({alarm:i,hour:parseInt(h),minute:parseInt(m),enabled:e})});alert('Alarm saved!');}loadAlarms();</script></body></html>";
}
String generateStatusPage() {
  return "<!DOCTYPE html><html><head><title>System Status</title><style>body{font-family:Arial;margin:20px;background:#f0f0f0;}.container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;}.section{border:1px solid #ddd;margin:10px 0;padding:15px;border-radius:5px;background:#f8f9fa;}.nav a{padding:10px 15px;background:#2196F3;color:white;text-decoration:none;margin:5px;border-radius:5px;}.item{margin:5px 0;display:flex;justify-content:space-between;}button{padding:8px 15px;background:#2196F3;color:white;border:none;border-radius:3px;cursor:pointer;}</style></head><body><div class='container'><h1 style='text-align:center;'>System Status</h1><div style='text-align:center;margin:20px 0;'><a href='/'>Home</a><a href='/alarms'>Alarms</a><a href='/status'>Status</a></div><div class='section'><h3>Time Information</h3><div class='item'><span>Current Time:</span><span id='time'>--:--:--</span></div><div class='item'><span>Current Date:</span><span id='date'>----/--/--</span></div><div class='item'><span>Time Sync:</span><span id='sync'>Checking...</span></div></div><div class='section'><h3>Network Information</h3><div class='item'><span>WiFi Status:</span><span id='wifi'>Checking...</span></div><div class='item'><span>IP Address:</span><span id='ip'>Getting...</span></div></div><div class='section'><h3>System Information</h3><div class='item'><span>Uptime:</span><span id='uptime'>Calculating...</span></div><div class='item'><span>Free Memory:</span><span id='memory'>Checking...</span></div><div class='item'><span>Chip Model:</span><span>ESP32-WROOM-32</span></div></div><div class='section'><h3>Alarm Status</h3><div id='alarms'></div></div><button onclick='update()' style='width:100%;margin-top:20px;'>Refresh Status</button></div><script>function update(){fetch('/api/status').then(r=>r.json()).then(d=>{document.getElementById('time').textContent=String(d.time.hour).padStart(2,'0')+':'+String(d.time.minute).padStart(2,'0')+':'+String(d.time.second).padStart(2,'0');document.getElementById('date').textContent=d.date.year+'/'+String(d.date.month).padStart(2,'0')+'/'+String(d.date.day).padStart(2,'0');document.getElementById('sync').textContent=d.wifi.timeSync?'Synced':'Not synced';document.getElementById('wifi').textContent=d.wifi.connected?'Connected':'Disconnected';document.getElementById('ip').textContent=d.wifi.connected?d.wifi.ip:'Not assigned';let uptime=d.system.uptime;let h=Math.floor(uptime/3600);let m=Math.floor((uptime%3600)/60);let s=uptime%60;document.getElementById('uptime').textContent=h+'h '+m+'m '+s+'s';document.getElementById('memory').textContent=Math.round(d.system.freeHeap/1024)+' KB';});fetch('/api/alarms').then(r=>r.json()).then(d=>{let html='';d.alarms.forEach((a,i)=>{html+='<div class=\"item\"><span>Alarm '+(i+1)+':</span><span>'+String(a.hour).padStart(2,'0')+':'+String(a.minute).padStart(2,'0')+' '+(a.enabled?'ON':'OFF')+'</span></div>';});document.getElementById('alarms').innerHTML=html;});}update();setInterval(update,5000);</script></body></html>";
}
