#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <time.h>
#include "config.h"

// === WiFi 状态变量 ===
extern bool wifiConnected;
extern bool timeSync;

// === 时间变量 ===
extern int currentHour;
extern int currentMinute;
extern int currentSecond;
extern int currentDay;
extern int currentMonth;
extern int currentYear;

// === WiFi 管理函数 ===
void scanWiFiNetworks();
void initWiFi();
void syncTimeFromNTP();
void updateTimeFromNTP();
void updateTimeFromMillis();
void printTimeStatus();

#endif // WIFI_MANAGER_H
