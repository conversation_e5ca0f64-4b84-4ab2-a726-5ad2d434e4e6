#ifndef CONFIG_H
#define CONFIG_H

// === GPIO 引脚定义（根据硬件设计文档） ===
// 电子墨水屏 SPI 接口
#define EPD_CS   26   // 片选
#define EPD_DC   27   // 数据/命令控制
#define EPD_RST  14   // 复位
#define EPD_BUSY 12   // 忙状态检测
#define EPD_SCK  18   // SPI 时钟
#define EPD_MOSI 23   // SPI 数据输出

// WS2812B RGB LED
#define LED_PIN     16  // RGB LED 控制引脚
#define NUM_LEDS    1   // LED 数量

// 蜂鸣器
#define BUZZER_PIN  25  // 无源蜂鸣器

// 触摸按键
#define KEY_A       33  // 触摸按键 A
#define KEY_B       13  // 触摸按键 B  
#define KEY_C       5   // 触摸按键 C

// === WiFi 配置 ===
extern const char* ssid;
extern const char* password;

// NTP 时间服务器配置
#define NTP_SERVER "pool.ntp.org"
#define GMT_OFFSET_SEC (8 * 3600)     // 中国时区 GMT+8
#define DAYLIGHT_OFFSET_SEC 0         // 夏令时偏移

// === 音符频率定义 ===
#define NOTE_C4  262   // 中央C (Do)
#define NOTE_D4  294   // D (Re)
#define NOTE_E4  330   // E (Mi)
#define NOTE_F4  349   // F (Fa)
#define NOTE_G4  392   // G (So)
#define NOTE_A4  440   // A (La)
#define NOTE_B4  494   // B (Si)
#define NOTE_C5  523   // 高音C

// === 系统配置 ===
#define SERIAL_BAUD 115200
#define ALARM_COUNT 3
#define SNOOZE_TIME_MS 300000  // 5分钟贪睡

#endif // CONFIG_H
