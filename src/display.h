#ifndef DISPLAY_H
#define DISPLAY_H

#include <Arduino.h>
#include <GxEPD2_BW.h>
#include <GxEPD2_3C.h>
#include <SPI.h>
#include <Fonts/FreeMonoBold12pt7b.h>
#include "config.h"
#include "graphics.h"

// === 显示对象 ===
extern GxEPD2_BW<GxEPD2_290_T94, GxEPD2_290_T94::HEIGHT> display;

// === 显示模式 ===
enum DisplayMode {
  DISPLAY_TIME = 0,
  DISPLAY_ALARM1 = 1,
  DISPLAY_ALARM2 = 2,
  DISPLAY_ALARM3 = 3
};

// === 显示状态变量 ===
extern int displayMode;
extern bool settingMode;
extern int settingItem;

// === 显示管理函数 ===
void initDisplay();
void updateClockDisplay();
void updateAlarmDisplay();
void updateAlarmRingingDisplay();
void showMessage(String line1, String line2 = "", String line3 = "");

// === 显示控制函数 ===
void switchDisplayMode();
void enterSettingMode();
void exitSettingMode();

// === 高级显示函数 ===
void updateAdvancedClockDisplay();
void showSystemStatus();
void showWeatherDisplay();
void showAnimatedClock();
void drawStatusIcons();

#endif // DISPLAY_H
