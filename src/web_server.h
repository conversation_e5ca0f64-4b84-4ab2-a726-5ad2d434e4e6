#ifndef WEB_SERVER_H
#define WEB_SERVER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <ArduinoJson.h>
#include "config.h"

// === Web服务器对象 ===
extern WebServer server;

// === Web服务器管理函数 ===
void initWebServer();
void handleWebServer();
void stopWebServer();

// === 网页处理函数 ===
void handleRoot();
void handleStatus();
void handleAlarms();
void handleSetAlarm();
void handleWiFiConfig();
void handleNotFound();

// === API接口函数 ===
void apiGetStatus();
void apiSetAlarm();
void apiToggleAlarm();
void apiGetAlarms();

// === 网页内容生成 ===
String generateMainPage();
String generateStatusPage();
String generateAlarmPage();
String generateSettingsPage();

#endif // WEB_SERVER_H
